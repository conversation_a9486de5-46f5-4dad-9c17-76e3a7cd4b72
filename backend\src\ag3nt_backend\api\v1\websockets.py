"""WebSocket endpoints for real-time updates."""

import json
import asyncio
from typing import Dict, Set
from datetime import datetime

from fastapi import <PERSON><PERSON>out<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

from ag3nt_backend.core.logging import get_logger
from ag3nt_backend.services.project_service import ProjectService


logger = get_logger(__name__)
router = APIRouter()

# Store active WebSocket connections
class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        # Map project_id to set of WebSocket connections
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.project_service = ProjectService()
    
    async def connect(self, websocket: WebSocket, project_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        
        if project_id not in self.active_connections:
            self.active_connections[project_id] = set()
        
        self.active_connections[project_id].add(websocket)
        logger.info("WebSocket connected", project_id=project_id, 
                   total_connections=len(self.active_connections[project_id]))
        
        # Send initial project status
        try:
            project = await self.project_service.get_project(project_id)
            if project:
                await self.send_personal_message({
                    "type": "project_status",
                    "data": {
                        "project_id": project_id,
                        "status": project["status"].value,
                        "progress": project["progress"],
                        "current_step": project["context"].get("current_step"),
                        "completed_steps": project["context"].get("completed_steps", []),
                    }
                }, websocket)
        except Exception as e:
            logger.error("Failed to send initial status", project_id=project_id, error=str(e))
    
    def disconnect(self, websocket: WebSocket, project_id: str):
        """Remove a WebSocket connection."""
        if project_id in self.active_connections:
            self.active_connections[project_id].discard(websocket)
            
            # Clean up empty project connections
            if not self.active_connections[project_id]:
                del self.active_connections[project_id]
            
            logger.info("WebSocket disconnected", project_id=project_id,
                       remaining_connections=len(self.active_connections.get(project_id, [])))
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific WebSocket."""
        try:
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(json.dumps({
                    **message,
                    "timestamp": datetime.now().isoformat()
                }))
        except Exception as e:
            logger.error("Failed to send personal message", error=str(e))
    
    async def broadcast_to_project(self, message: dict, project_id: str):
        """Broadcast a message to all connections for a project."""
        if project_id not in self.active_connections:
            return
        
        message_with_timestamp = {
            **message,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to all connections for this project
        disconnected_connections = set()
        for connection in self.active_connections[project_id]:
            try:
                if connection.client_state == WebSocketState.CONNECTED:
                    await connection.send_text(json.dumps(message_with_timestamp))
                else:
                    disconnected_connections.add(connection)
            except Exception as e:
                logger.error("Failed to broadcast message", project_id=project_id, error=str(e))
                disconnected_connections.add(connection)
        
        # Clean up disconnected connections
        for connection in disconnected_connections:
            self.active_connections[project_id].discard(connection)
    
    async def send_step_update(self, project_id: str, step: str, status: str, result: dict = None):
        """Send a step update to all project connections."""
        await self.broadcast_to_project({
            "type": "step_update",
            "data": {
                "project_id": project_id,
                "step": step,
                "status": status,
                "result": result,
            }
        }, project_id)
    
    async def send_progress_update(self, project_id: str, progress: float, current_step: str = None):
        """Send a progress update to all project connections."""
        await self.broadcast_to_project({
            "type": "progress_update",
            "data": {
                "project_id": project_id,
                "progress": progress,
                "current_step": current_step,
            }
        }, project_id)
    
    async def send_error_update(self, project_id: str, error_message: str, step: str = None):
        """Send an error update to all project connections."""
        await self.broadcast_to_project({
            "type": "error_update",
            "data": {
                "project_id": project_id,
                "error_message": error_message,
                "step": step,
            }
        }, project_id)
    
    def get_connection_count(self, project_id: str = None) -> int:
        """Get the number of active connections."""
        if project_id:
            return len(self.active_connections.get(project_id, []))
        else:
            return sum(len(connections) for connections in self.active_connections.values())


# Global connection manager
manager = ConnectionManager()


@router.websocket("/projects/{project_id}")
async def websocket_endpoint(websocket: WebSocket, project_id: str):
    """WebSocket endpoint for real-time project updates."""
    await manager.connect(websocket, project_id)
    
    try:
        while True:
            # Keep the connection alive and handle incoming messages
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type")
                
                if message_type == "ping":
                    # Respond to ping with pong
                    await manager.send_personal_message({
                        "type": "pong",
                        "data": {"message": "Connection alive"}
                    }, websocket)
                
                elif message_type == "request_status":
                    # Send current project status
                    project = await manager.project_service.get_project(project_id)
                    if project:
                        await manager.send_personal_message({
                            "type": "project_status",
                            "data": {
                                "project_id": project_id,
                                "status": project["status"].value,
                                "progress": project["progress"],
                                "current_step": project["context"].get("current_step"),
                                "completed_steps": project["context"].get("completed_steps", []),
                                "error_message": project.get("error_message"),
                            }
                        }, websocket)
                
                else:
                    logger.warning("Unknown message type", message_type=message_type, project_id=project_id)
                    
            except json.JSONDecodeError:
                logger.error("Invalid JSON received", project_id=project_id, data=data)
            except Exception as e:
                logger.error("Error processing WebSocket message", project_id=project_id, error=str(e))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, project_id)
    except Exception as e:
        logger.error("WebSocket error", project_id=project_id, error=str(e))
        manager.disconnect(websocket, project_id)


# Export the manager for use in other modules
def get_connection_manager() -> ConnectionManager:
    """Get the global connection manager."""
    return manager
