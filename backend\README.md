# AG3NT Backend - CrewAI + LangGraph Architecture

## Overview

This Python backend replaces the custom AG3NT framework with a robust multi-agent system using:
- **CrewAI** for multi-agent collaboration and orchestration
- **LangGraph** for explicit workflow control and state management
- **Context7** for enhanced context management and RAG capabilities
- **FastAPI** for high-performance API endpoints

## Architecture

### Multi-Agent System (CrewAI)

#### Core Agents
1. **Project Planning Agent**
   - Role: Senior Project Analyst
   - Goal: Analyze project requirements and create comprehensive plans
   - Tools: Context7 retrieval, document analysis, requirement extraction

2. **Task Planning Agent**
   - Role: Task Breakdown Specialist
   - Goal: Break down projects into actionable development tasks
   - Tools: Task estimation, dependency analysis, priority scoring

3. **Code Generation Agent**
   - Role: Senior Software Engineer
   - Goal: Generate high-quality code and technical artifacts
   - Tools: Code generation, file structure planning, documentation

4. **Analysis Agent**
   - Role: Data Analyst & Researcher
   - Goal: Perform market research, competitive analysis, and insights
   - Tools: Web search, data analysis, report generation

5. **Context Management Agent**
   - Role: Knowledge Curator
   - Goal: Manage project context, memory, and knowledge retrieval
   - Tools: Context7 integration, memory management, RAG operations

#### Agent Crews
- **Planning Crew**: Project Planning + Task Planning + Context Management
- **Development Crew**: Code Generation + Analysis + Context Management
- **Research Crew**: Analysis + Context Management

### Workflow System (LangGraph)

#### Core Workflows
1. **Project Initialization Workflow**
   ```
   Start → Analyze Requirements → Clarify Needs → Generate Summary → 
   Select Tech Stack → Create PRD → End
   ```

2. **Iterative Planning Workflow**
   ```
   Start → Plan Phase → Execute Phase → Review Phase → 
   [Continue/Refine/Complete] → End
   ```

3. **Code Review Workflow**
   ```
   Start → Generate Code → Review Code → Test Code → 
   [Approve/Revise] → Deploy → End
   ```

4. **Research & Analysis Workflow**
   ```
   Start → Gather Data → Analyze Trends → Generate Insights → 
   Create Report → End
   ```

### State Management

#### Shared State Schema
```python
class ProjectState(TypedDict):
    project_id: str
    user_prompt: str
    requirements: Dict[str, Any]
    tech_stack: Dict[str, Any]
    tasks: List[Dict[str, Any]]
    artifacts: List[Dict[str, Any]]
    context: Dict[str, Any]
    status: str
    progress: float
```

#### Workflow-Specific States
- `PlanningState`: Extends ProjectState with planning-specific fields
- `DevelopmentState`: Extends ProjectState with code generation fields
- `ReviewState`: Extends ProjectState with review and feedback fields

### API Architecture

#### FastAPI Endpoints
- `POST /api/projects/initialize` - Start new project workflow
- `POST /api/projects/{id}/plan` - Execute planning workflow
- `POST /api/projects/{id}/develop` - Execute development workflow
- `POST /api/projects/{id}/review` - Execute review workflow
- `GET /api/projects/{id}/status` - Get project status and progress
- `WebSocket /ws/projects/{id}` - Real-time updates

#### Integration with Next.js Frontend
- Replace existing `/api/planning/*` routes with Python backend calls
- Maintain existing UI components with updated API calls
- Add WebSocket support for real-time agent communication
- Preserve existing state management patterns in React

## Benefits of New Architecture

### Compared to Current AG3NT Framework

1. **Specialized Agents**: Instead of one AI service, multiple specialized agents
2. **Explicit Workflows**: LangGraph provides clear, stateful workflow control
3. **Better Collaboration**: CrewAI enables agents to work together effectively
4. **Enhanced Context**: Context7 provides superior RAG and knowledge management
5. **Scalability**: Python backend can scale independently of frontend
6. **Reliability**: Built-in error handling, retries, and state persistence
7. **Extensibility**: Easy to add new agents and workflows

### Enterprise Features
- **Human-in-the-Loop**: LangGraph supports approval gates and human feedback
- **Audit Trail**: Complete workflow execution history
- **Error Recovery**: Automatic retry and fallback mechanisms
- **Performance Monitoring**: Built-in metrics and observability
- **Security**: Proper authentication and authorization

## Migration Strategy

1. **Phase 1**: Set up Python backend with basic FastAPI structure
2. **Phase 2**: Implement core agents using CrewAI
3. **Phase 3**: Create LangGraph workflows for existing planning steps
4. **Phase 4**: Integrate Context7 for enhanced context management
5. **Phase 5**: Update Next.js frontend to use new backend APIs
6. **Phase 6**: Add advanced features (WebSockets, human-in-the-loop, etc.)

## Technology Stack

- **Python 3.11+**
- **FastAPI** - High-performance web framework
- **CrewAI** - Multi-agent orchestration
- **LangGraph** - Workflow management
- **Context7** - Enhanced context and RAG
- **Pydantic** - Data validation and serialization
- **SQLAlchemy** - Database ORM (for state persistence)
- **Redis** - Caching and session management
- **WebSockets** - Real-time communication

This architecture provides a solid foundation for scaling your agent system while maintaining the excellent user experience of your existing Next.js frontend.
