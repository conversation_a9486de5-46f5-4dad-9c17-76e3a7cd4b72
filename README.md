# AG3NT - Multi-Agent Project Planning System

An intelligent AI-powered project planning tool that uses **CrewAI** and **LangGraph** to transform ideas into comprehensive project plans with detailed implementation guidance.

## 🚀 New Architecture (v2.0)

AG3NT has been completely refactored to use a modern multi-agent architecture:

- **🤖 CrewAI Multi-Agent System**: Specialized agents working together
- **🔄 LangGraph Workflows**: Explicit, stateful workflow control
- **🧠 Context7 Integration**: Enhanced context management and RAG
- **🐍 Python Backend**: High-performance FastAPI backend
- **⚛️ Next.js Frontend**: Maintained excellent user experience

## ✨ Features

- **🎯 Multi-Agent Collaboration**: Specialized agents for planning, analysis, and code generation
- **🔄 Explicit Workflows**: LangGraph-powered state management with checkpoints
- **🧠 Enhanced Context**: Context7 integration for superior knowledge management
- **📱 Interactive & Autonomous Modes**: Choose between guided questions or fully automated planning
- **🖼️ Image-Based Design Analysis**: Upload reference images for automatic style guide generation
- **📊 Real-time Processing**: Watch as each planning step completes with agent collaboration
- **📋 Professional Output**: Generates detailed documentation, wireframes, and implementation guides
- **🔍 Context Search**: Search across projects and knowledge base
- **📈 Performance Metrics**: Agent and workflow performance tracking

## New: Image Upload Feature

The Planning Agent now supports uploading reference images to automatically generate detailed design style guides:

### How it works:
1. **Upload Images**: Click the paperclip icon in the input field to upload design reference images
2. **AI Analysis**: Images are analyzed by Google Gemini 2.5 Pro to extract comprehensive design information
3. **Style Guide Generation**: A detailed style guide is automatically created covering:
   - Color palettes with hex codes and RGB values
   - Typography specifications (fonts, weights, sizes, spacing)
   - Layout and spacing patterns
   - Component styling details
   - Visual effects and design principles

### Benefits:
- **Skip Manual Design Work**: No need to manually create design guidelines
- **Accurate Analysis**: AI extracts precise color codes, measurements, and styling details
- **Professional Documentation**: Generated style guides are developer-ready
- **Visual Context**: The planning process considers your specific design preferences

## Planning Steps

1. **Analyze Requirements** - Extract project details and scope
2. **Gather Details** - Interactive clarification questions (if enabled)
3. **Generate Summary** - Comprehensive project overview
4. **Select Tech Stack** - Recommended technologies and frameworks
5. **Create Requirements** - Detailed PRD with specifications
6. **Design Wireframes** - ASCII-based UI layouts
7. **Design Guidelines** - Style guide (auto-generated from images if uploaded)
8. **Database Schema** - Data structure and relationships
9. **File Structure** - Organized project architecture
10. **Workflow Logic** - Business process definitions
11. **Implementation Tasks** - Detailed development breakdown
12. **Project Scaffold** - Ready-to-use code structure

## 🏗️ Architecture Overview

### Multi-Agent System (CrewAI)
- **Project Planning Agent**: Requirements analysis and project planning
- **Task Planning Agent**: Task breakdown and estimation
- **Code Generation Agent**: Code and technical artifact generation
- **Analysis Agent**: Market research and competitive analysis
- **Context Management Agent**: Knowledge curation and context management

### Workflows (LangGraph)
- **Planning Workflow**: Project initialization and comprehensive planning
- **Development Workflow**: Code generation and review cycles
- **Research Workflow**: Market analysis and insights generation

### Context Management (Context7)
- **Enhanced RAG**: Advanced retrieval-augmented generation
- **Project Memory**: Persistent context across sessions
- **Knowledge Search**: Searchable project knowledge base

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**Linux/macOS:**
```bash
./start-dev.sh
```

**Windows:**
```batch
start-dev.bat
```

### Option 2: Manual Setup

1. **Clone and install frontend dependencies:**
```bash
git clone <repository-url>
cd AG3NT
npm install
cp .env.example .env
# Configure your API keys in .env
```

2. **Set up Python backend:**
```bash
cd backend
pip install poetry
poetry install
cp .env.example .env
# Configure your API keys in backend/.env
```

3. **Start both services:**

**Terminal 1 - Backend:**
```bash
cd backend
python run.py
```

**Terminal 2 - Frontend:**
```bash
npm run dev
```

4. **Open your browser:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## ⚙️ Configuration

### Required API Keys

Add these to both `.env` and `backend/.env`:

```bash
# LLM APIs (at least one required)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
OPENROUTER_API_KEY=your_openrouter_key

# CrewAI (optional, for enhanced features)
CREWAI_API_KEY=your_crewai_key

# Context7 (optional, for enhanced context management)
CONTEXT7_API_KEY=your_context7_key

# Backend URL (for frontend)
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
```

## Setup (Legacy)

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables:
   ```
   OPENROUTER_API_KEY=your_openrouter_api_key
   ```
4. Run the development server: `npm run dev`
5. Open [http://localhost:3000](http://localhost:3000)

## Usage

1. Enter your project idea in the input field
2. Optionally upload reference images using the paperclip icon
3. Choose between Interactive or Autonomous mode
4. Watch as the AI processes each planning step
5. Review the comprehensive results with all planning artifacts

## Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **AI Services**: 
  - OpenRouter API (Claude 3.5 Sonnet for planning)
  - Google Gemini 2.5 Pro (for image analysis)
- **UI Components**: Radix UI, Lucide Icons, Framer Motion
- **Styling**: Tailwind CSS with custom red theme

## Environment Variables

- `OPENROUTER_API_KEY`: Your OpenRouter API key for AI services
- `NEXT_PUBLIC_SITE_URL`: Your site URL (for OpenRouter headers)
