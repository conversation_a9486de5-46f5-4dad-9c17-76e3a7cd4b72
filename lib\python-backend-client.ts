/**
 * Client for communicating with the Python backend
 * This replaces the existing AI service and planning engine
 */

export interface ProjectCreateRequest {
  prompt: string
  isInteractive?: boolean
  answers?: Record<string, any>
  designStyleGuide?: Record<string, any>
  hasImages?: boolean
}

export interface ProjectResponse {
  project_id: string
  status: string
  progress: number
  user_prompt: string
  created_at: string
  updated_at: string
  results?: Record<string, any>
  error_message?: string
}

export interface ProjectStepRequest {
  step: string
  context?: Record<string, any>
  answer?: string
}

export interface ProjectStepResponse {
  project_id: string
  step: string
  status: string
  result: Record<string, any>
  needs_input?: boolean
  question?: string
  next_step?: string
}

export interface AgentInfo {
  agent_id: string
  role: string
  name: string
  goal: string
  backstory: string
  current_task?: string
  task_count: number
  performance_metrics: Record<string, number>
  last_action?: string
}

export interface WorkflowInfo {
  workflow_id: string
  workflow_name: string
  description: string
  status: string
  steps: string[]
  current_step?: string
  progress: number
}

class PythonBackendClient {
  private baseUrl: string
  private apiKey?: string

  constructor(baseUrl: string = 'http://localhost:8000', apiKey?: string) {
    this.baseUrl = baseUrl
    this.apiKey = apiKey
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api/v1${endpoint}`
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...((options.headers as Record<string, string>) || {}),
    }

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`
      )
    }

    return response.json()
  }

  // Project Management
  async createProject(request: ProjectCreateRequest): Promise<ProjectResponse> {
    return this.request<ProjectResponse>('/projects', {
      method: 'POST',
      body: JSON.stringify({
        prompt: request.prompt,
        is_interactive: request.isInteractive ?? true,
        answers: request.answers ?? {},
        design_style_guide: request.designStyleGuide,
        has_images: request.hasImages ?? false,
      }),
    })
  }

  async getProject(projectId: string): Promise<ProjectResponse> {
    return this.request<ProjectResponse>(`/projects/${projectId}`)
  }

  async executeProjectStep(
    projectId: string,
    request: ProjectStepRequest
  ): Promise<ProjectStepResponse> {
    return this.request<ProjectStepResponse>(`/projects/${projectId}/steps`, {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  async getProjectStatus(projectId: string): Promise<{
    project_id: string
    status: string
    progress: number
    current_step?: string
    completed_steps: string[]
    error_message?: string
    updated_at: string
  }> {
    return this.request(`/projects/${projectId}/status`)
  }

  async deleteProject(projectId: string): Promise<{ message: string }> {
    return this.request(`/projects/${projectId}`, {
      method: 'DELETE',
    })
  }

  // Agent Management
  async listAgents(): Promise<AgentInfo[]> {
    return this.request<AgentInfo[]>('/agents')
  }

  async getAgent(agentId: string): Promise<AgentInfo> {
    return this.request<AgentInfo>(`/agents/${agentId}`)
  }

  async getAgentStatus(agentId: string): Promise<{
    agent_id: string
    status: string
    current_task?: string
    task_queue_length: number
    performance_metrics: Record<string, number>
    last_activity?: string
  }> {
    return this.request(`/agents/${agentId}/status`)
  }

  // Workflow Management
  async listWorkflows(): Promise<WorkflowInfo[]> {
    return this.request<WorkflowInfo[]>('/workflows')
  }

  async getWorkflow(workflowId: string): Promise<WorkflowInfo> {
    return this.request<WorkflowInfo>(`/workflows/${workflowId}`)
  }

  async getWorkflowStatus(workflowId: string): Promise<{
    workflow_id: string
    status: string
    current_step?: string
    progress: number
    execution_time: number
    last_execution?: string
    error_message?: string
  }> {
    return this.request(`/workflows/${workflowId}/status`)
  }

  // Context Management
  async getProjectContext(projectId: string): Promise<{
    project_id: string
    context: Record<string, any>
    metadata: Record<string, any>
  }> {
    return this.request(`/context/projects/${projectId}`)
  }

  async updateProjectContext(
    projectId: string,
    contextUpdate: Record<string, any>
  ): Promise<{ message: string }> {
    return this.request(`/context/projects/${projectId}/update`, {
      method: 'POST',
      body: JSON.stringify(contextUpdate),
    })
  }

  async searchContext(
    query: string,
    projectId?: string,
    contextType?: string,
    limit: number = 10
  ): Promise<any[]> {
    const params = new URLSearchParams({
      query,
      limit: limit.toString(),
    })
    
    if (projectId) params.append('project_id', projectId)
    if (contextType) params.append('context_type', contextType)

    return this.request(`/context/search?${params}`)
  }

  async deleteProjectContext(projectId: string): Promise<{ message: string }> {
    return this.request(`/context/projects/${projectId}`, {
      method: 'DELETE',
    })
  }

  // Health Check
  async healthCheck(): Promise<{
    status: string
    version: string
    service: string
  }> {
    const response = await fetch(`${this.baseUrl}/health`)
    return response.json()
  }

  // WebSocket connection for real-time updates
  createWebSocket(projectId: string): WebSocket {
    const wsUrl = this.baseUrl.replace('http', 'ws')
    return new WebSocket(`${wsUrl}/ws/projects/${projectId}`)
  }
}

// Create a singleton instance
export const pythonBackendClient = new PythonBackendClient(
  process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'
)

// Legacy compatibility - map old functions to new client
export const createProject = (request: ProjectCreateRequest) => 
  pythonBackendClient.createProject(request)

export const getProject = (projectId: string) => 
  pythonBackendClient.getProject(projectId)

export const executeStep = (projectId: string, request: ProjectStepRequest) => 
  pythonBackendClient.executeProjectStep(projectId, request)

export const getProjectStatus = (projectId: string) => 
  pythonBackendClient.getProjectStatus(projectId)

export default pythonBackendClient
