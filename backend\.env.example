# Environment Configuration for AG3NT Backend

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true
API_WORKERS=1

# Database Configuration
DATABASE_URL=sqlite:///./ag3nt.db
# For PostgreSQL: postgresql://user:password@localhost/ag3nt
# For MySQL: mysql://user:password@localhost/ag3nt

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# LLM API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# CrewAI Configuration
CREWAI_API_KEY=your_crewai_api_key_here
CREWAI_TELEMETRY_OPT_OUT=true

# LangChain Configuration
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your_langchain_api_key_here
LANGCHAIN_PROJECT=ag3nt-backend

# Context7 Configuration (if using)
CONTEXT7_API_KEY=your_context7_api_key_here
CONTEXT7_BASE_URL=https://api.context7.com

# Security Configuration
SECRET_KEY=your_super_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Application Configuration
APP_NAME=AG3NT Backend
APP_VERSION=0.1.0
DEBUG=false
TESTING=false

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# Agent Configuration
DEFAULT_LLM_MODEL=gpt-4o
DEFAULT_LLM_TEMPERATURE=0.7
MAX_AGENT_ITERATIONS=10
AGENT_TIMEOUT=300  # seconds

# Workflow Configuration
MAX_WORKFLOW_DURATION=3600  # 1 hour in seconds
WORKFLOW_CHECKPOINT_INTERVAL=60  # seconds

# Development Configuration
RELOAD_DIRS=["src"]
RELOAD_INCLUDES=["*.py"]
RELOAD_EXCLUDES=["*.pyc", "__pycache__"]
