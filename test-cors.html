<!DOCTYPE html>
<html>
<head>
    <title>AG3NT CORS Test</title>
</head>
<body>
    <h1>AG3NT CORS Test</h1>
    <button onclick="testBackend()">Test Backend Connection</button>
    <div id="result"></div>

    <script>
        async function testBackend() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                // Test health endpoint
                const healthResponse = await fetch('http://localhost:8000/health');
                const healthData = await healthResponse.json();
                console.log('Health check:', healthData);
                
                // Test project creation
                const projectData = {
                    prompt: "Test project from CORS test",
                    is_interactive: false,
                    answers: {},
                    has_images: false
                };
                
                const projectResponse = await fetch('http://localhost:8000/api/v1/projects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(projectData)
                });
                
                const projectResult = await projectResponse.json();
                console.log('Project creation:', projectResult);
                
                resultDiv.innerHTML = `
                    <h3>✅ Success!</h3>
                    <p><strong>Health:</strong> ${healthData.status}</p>
                    <p><strong>Project ID:</strong> ${projectResult.project_id}</p>
                    <p><strong>Status:</strong> ${projectResult.status}</p>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Error!</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
