"""Agent management API endpoints."""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ag3nt_backend.core.logging import get_logger


logger = get_logger(__name__)
router = APIRouter()


class AgentResponse(BaseModel):
    """Response model for agent data."""
    agent_id: str
    role: str
    name: str
    goal: str
    backstory: str
    current_task: str = None
    task_count: int = 0
    performance_metrics: Dict[str, float] = {}
    last_action: str = None


@router.get("/", response_model=List[AgentResponse])
async def list_agents() -> List[AgentResponse]:
    """List all available agents."""
    # This would return actual agent instances in a real implementation
    agents = [
        AgentResponse(
            agent_id="project_planner_001",
            role="project_planner",
            name="ProjectPlanningAgent",
            goal="Analyze project requirements and create comprehensive project plans",
            backstory="Senior project analyst with 10+ years of experience",
            current_task=None,
            task_count=0,
            performance_metrics={},
            last_action=None,
        ),
        AgentResponse(
            agent_id="task_planner_001",
            role="task_planner", 
            name="TaskPlanningAgent",
            goal="Break down projects into detailed, actionable development tasks",
            backstory="Experienced technical project manager and scrum master",
            current_task=None,
            task_count=0,
            performance_metrics={},
            last_action=None,
        ),
    ]
    
    return agents


@router.get("/{agent_id}", response_model=AgentResponse)
async def get_agent(agent_id: str) -> AgentResponse:
    """Get agent details by ID."""
    # This would fetch actual agent data in a real implementation
    if agent_id == "project_planner_001":
        return AgentResponse(
            agent_id=agent_id,
            role="project_planner",
            name="ProjectPlanningAgent",
            goal="Analyze project requirements and create comprehensive project plans",
            backstory="Senior project analyst with 10+ years of experience",
            current_task=None,
            task_count=0,
            performance_metrics={},
            last_action=None,
        )
    else:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")


@router.get("/{agent_id}/status")
async def get_agent_status(agent_id: str) -> Dict[str, Any]:
    """Get agent status and metrics."""
    return {
        "agent_id": agent_id,
        "status": "idle",
        "current_task": None,
        "task_queue_length": 0,
        "performance_metrics": {
            "tasks_completed": 0,
            "average_completion_time": 0.0,
            "success_rate": 1.0,
        },
        "last_activity": None,
    }
