"""Task Planning Agent implementation."""

from typing import Any, Dict, List
import json
from datetime import datetime, timed<PERSON><PERSON>

from crewai_tools import <PERSON><PERSON>ool
from pydantic import BaseModel, Field

from ag3nt_backend.agents.base_agent import BaseAG3NTAgent
from ag3nt_backend.models.state import Agent<PERSON><PERSON>, TaskStatus
from ag3nt_backend.core.exceptions import AgentError


class TaskBreakdownTool(BaseTool):
    """Tool for breaking down projects into tasks."""
    
    name: str = "task_breakdown_tool"
    description: str = "Break down project requirements into actionable development tasks"
    
    def _run(self, project_summary: str, tech_stack: str = "{}") -> str:
        """Break down project into tasks."""
        try:
            summary_data = json.loads(project_summary) if isinstance(project_summary, str) else project_summary
            tech_data = json.loads(tech_stack) if isinstance(tech_stack, str) else tech_stack
        except json.JSONDecodeError:
            return json.dumps({"tasks": []})
        
        tasks = []
        task_id = 1
        
        # Project setup tasks
        tasks.extend(self._generate_setup_tasks(summary_data, tech_data, task_id))
        task_id += len(tasks)
        
        # Feature development tasks
        features = summary_data.get("key_features", [])
        for feature in features:
            feature_tasks = self._generate_feature_tasks(feature, summary_data, tech_data, task_id)
            tasks.extend(feature_tasks)
            task_id += len(feature_tasks)
        
        # Testing and deployment tasks
        tasks.extend(self._generate_testing_tasks(summary_data, tech_data, task_id))
        task_id += len(tasks)
        
        tasks.extend(self._generate_deployment_tasks(summary_data, tech_data, task_id))
        
        return json.dumps({"tasks": tasks})
    
    def _generate_setup_tasks(self, summary: Dict[str, Any], tech_stack: Dict[str, Any], start_id: int) -> List[Dict[str, Any]]:
        """Generate project setup tasks."""
        tasks = []
        
        # Basic setup tasks
        tasks.append({
            "id": f"task_{start_id}",
            "title": "Project Setup and Configuration",
            "description": "Initialize project structure, configure development environment, and set up version control",
            "category": "setup",
            "priority": "high",
            "estimated_hours": 4,
            "dependencies": [],
            "status": TaskStatus.PENDING.value,
            "tags": ["setup", "configuration"],
        })
        
        tasks.append({
            "id": f"task_{start_id + 1}",
            "title": "Database Design and Setup",
            "description": "Design database schema, set up database connections, and create initial migrations",
            "category": "setup",
            "priority": "high",
            "estimated_hours": 6,
            "dependencies": [f"task_{start_id}"],
            "status": TaskStatus.PENDING.value,
            "tags": ["database", "schema"],
        })
        
        # Add authentication setup if needed
        if "authentication" in summary.get("key_features", []):
            tasks.append({
                "id": f"task_{start_id + 2}",
                "title": "Authentication System Setup",
                "description": "Set up user authentication, session management, and security middleware",
                "category": "setup",
                "priority": "high",
                "estimated_hours": 8,
                "dependencies": [f"task_{start_id + 1}"],
                "status": TaskStatus.PENDING.value,
                "tags": ["authentication", "security"],
            })
        
        return tasks
    
    def _generate_feature_tasks(self, feature: str, summary: Dict[str, Any], tech_stack: Dict[str, Any], start_id: int) -> List[Dict[str, Any]]:
        """Generate tasks for a specific feature."""
        tasks = []
        
        if feature == "authentication":
            tasks.extend(self._generate_auth_tasks(start_id))
        elif feature == "data_management":
            tasks.extend(self._generate_data_tasks(start_id))
        elif feature == "payment_processing":
            tasks.extend(self._generate_payment_tasks(start_id))
        elif feature == "notifications":
            tasks.extend(self._generate_notification_tasks(start_id))
        else:
            # Generic feature task
            tasks.append({
                "id": f"task_{start_id}",
                "title": f"Implement {feature.replace('_', ' ').title()}",
                "description": f"Develop and implement the {feature.replace('_', ' ')} functionality",
                "category": "feature",
                "priority": "medium",
                "estimated_hours": 12,
                "dependencies": [],
                "status": TaskStatus.PENDING.value,
                "tags": ["feature", feature],
            })
        
        return tasks
    
    def _generate_auth_tasks(self, start_id: int) -> List[Dict[str, Any]]:
        """Generate authentication-related tasks."""
        return [
            {
                "id": f"task_{start_id}",
                "title": "User Registration System",
                "description": "Implement user registration with email verification",
                "category": "feature",
                "priority": "high",
                "estimated_hours": 8,
                "dependencies": [],
                "status": TaskStatus.PENDING.value,
                "tags": ["authentication", "registration"],
            },
            {
                "id": f"task_{start_id + 1}",
                "title": "User Login System",
                "description": "Implement secure user login with session management",
                "category": "feature",
                "priority": "high",
                "estimated_hours": 6,
                "dependencies": [f"task_{start_id}"],
                "status": TaskStatus.PENDING.value,
                "tags": ["authentication", "login"],
            },
            {
                "id": f"task_{start_id + 2}",
                "title": "Password Reset Functionality",
                "description": "Implement secure password reset via email",
                "category": "feature",
                "priority": "medium",
                "estimated_hours": 4,
                "dependencies": [f"task_{start_id + 1}"],
                "status": TaskStatus.PENDING.value,
                "tags": ["authentication", "password"],
            },
        ]
    
    def _generate_data_tasks(self, start_id: int) -> List[Dict[str, Any]]:
        """Generate data management tasks."""
        return [
            {
                "id": f"task_{start_id}",
                "title": "Data Models Implementation",
                "description": "Create and implement core data models and relationships",
                "category": "feature",
                "priority": "high",
                "estimated_hours": 10,
                "dependencies": [],
                "status": TaskStatus.PENDING.value,
                "tags": ["data", "models"],
            },
            {
                "id": f"task_{start_id + 1}",
                "title": "CRUD Operations",
                "description": "Implement Create, Read, Update, Delete operations for all entities",
                "category": "feature",
                "priority": "high",
                "estimated_hours": 12,
                "dependencies": [f"task_{start_id}"],
                "status": TaskStatus.PENDING.value,
                "tags": ["data", "crud"],
            },
        ]
    
    def _generate_payment_tasks(self, start_id: int) -> List[Dict[str, Any]]:
        """Generate payment processing tasks."""
        return [
            {
                "id": f"task_{start_id}",
                "title": "Payment Gateway Integration",
                "description": "Integrate with payment provider (Stripe/PayPal) for processing payments",
                "category": "feature",
                "priority": "high",
                "estimated_hours": 16,
                "dependencies": [],
                "status": TaskStatus.PENDING.value,
                "tags": ["payment", "integration"],
            },
            {
                "id": f"task_{start_id + 1}",
                "title": "Subscription Management",
                "description": "Implement subscription plans and billing management",
                "category": "feature",
                "priority": "medium",
                "estimated_hours": 12,
                "dependencies": [f"task_{start_id}"],
                "status": TaskStatus.PENDING.value,
                "tags": ["payment", "subscription"],
            },
        ]
    
    def _generate_notification_tasks(self, start_id: int) -> List[Dict[str, Any]]:
        """Generate notification tasks."""
        return [
            {
                "id": f"task_{start_id}",
                "title": "Email Notification System",
                "description": "Set up email templates and notification delivery system",
                "category": "feature",
                "priority": "medium",
                "estimated_hours": 8,
                "dependencies": [],
                "status": TaskStatus.PENDING.value,
                "tags": ["notifications", "email"],
            },
        ]
    
    def _generate_testing_tasks(self, summary: Dict[str, Any], tech_stack: Dict[str, Any], start_id: int) -> List[Dict[str, Any]]:
        """Generate testing tasks."""
        return [
            {
                "id": f"task_{start_id}",
                "title": "Unit Testing Implementation",
                "description": "Write comprehensive unit tests for all core functionality",
                "category": "testing",
                "priority": "high",
                "estimated_hours": 16,
                "dependencies": [],
                "status": TaskStatus.PENDING.value,
                "tags": ["testing", "unit-tests"],
            },
            {
                "id": f"task_{start_id + 1}",
                "title": "Integration Testing",
                "description": "Implement integration tests for API endpoints and database operations",
                "category": "testing",
                "priority": "medium",
                "estimated_hours": 12,
                "dependencies": [f"task_{start_id}"],
                "status": TaskStatus.PENDING.value,
                "tags": ["testing", "integration"],
            },
        ]
    
    def _generate_deployment_tasks(self, summary: Dict[str, Any], tech_stack: Dict[str, Any], start_id: int) -> List[Dict[str, Any]]:
        """Generate deployment tasks."""
        return [
            {
                "id": f"task_{start_id}",
                "title": "Production Environment Setup",
                "description": "Configure production environment, CI/CD pipeline, and monitoring",
                "category": "deployment",
                "priority": "high",
                "estimated_hours": 12,
                "dependencies": [],
                "status": TaskStatus.PENDING.value,
                "tags": ["deployment", "production"],
            },
            {
                "id": f"task_{start_id + 1}",
                "title": "Security Hardening",
                "description": "Implement security best practices and vulnerability scanning",
                "category": "deployment",
                "priority": "high",
                "estimated_hours": 8,
                "dependencies": [f"task_{start_id}"],
                "status": TaskStatus.PENDING.value,
                "tags": ["deployment", "security"],
            },
        ]


class TaskPlanningAgent(BaseAG3NTAgent):
    """Agent responsible for breaking down projects into actionable tasks."""
    
    def __init__(self):
        super().__init__(
            role=AgentRole.TASK_PLANNER,
            goal="Break down projects into detailed, actionable development tasks with accurate estimates",
            backstory="""You are an experienced technical project manager and scrum master 
            with deep knowledge of software development processes. You excel at breaking 
            down complex projects into manageable tasks, estimating effort accurately, 
            and identifying dependencies between tasks.""",
            tools=self.get_specialized_tools(),
        )
    
    def get_specialized_tools(self) -> List[BaseTool]:
        """Get tools specific to task planning."""
        return [
            TaskBreakdownTool(),
        ]
    
    async def _execute_task_impl(self, task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task planning task."""
        try:
            task_type = context.get("task_type", "breakdown")
            
            if task_type == "breakdown":
                return await self._breakdown_tasks(context)
            elif task_type == "estimate":
                return await self._estimate_tasks(context)
            elif task_type == "prioritize":
                return await self._prioritize_tasks(context)
            else:
                raise AgentError(f"Unknown task type: {task_type}")
                
        except Exception as e:
            self.log_error(f"Task planning failed", task_type=task_type, error=str(e))
            raise
    
    async def _breakdown_tasks(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Break down project into tasks."""
        project_summary = context.get("project_summary", {})
        tech_stack = context.get("tech_stack", {})
        
        self.log_info("Breaking down project into tasks")
        
        # Use the breakdown tool
        breakdown_tool = TaskBreakdownTool()
        tasks_result = breakdown_tool._run(
            json.dumps(project_summary),
            json.dumps(tech_stack)
        )
        
        try:
            tasks_data = json.loads(tasks_result)
            tasks = tasks_data.get("tasks", [])
            
            # Add timeline estimates
            self._add_timeline_estimates(tasks)
            
            self.log_info("Task breakdown completed", task_count=len(tasks))
            return {
                "tasks": tasks,
                "total_estimated_hours": sum(task.get("estimated_hours", 0) for task in tasks),
                "task_categories": list(set(task.get("category", "unknown") for task in tasks)),
            }
        except json.JSONDecodeError:
            raise AgentError("Failed to parse task breakdown result")
    
    async def _estimate_tasks(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Refine task estimates."""
        tasks = context.get("tasks", [])
        
        self.log_info("Refining task estimates", task_count=len(tasks))
        
        # Apply estimation refinements
        for task in tasks:
            self._refine_task_estimate(task)
        
        return {"tasks": tasks}
    
    async def _prioritize_tasks(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prioritize tasks based on dependencies and importance."""
        tasks = context.get("tasks", [])
        
        self.log_info("Prioritizing tasks", task_count=len(tasks))
        
        # Calculate priority scores
        for task in tasks:
            task["priority_score"] = self._calculate_priority_score(task, tasks)
        
        # Sort by priority score
        sorted_tasks = sorted(tasks, key=lambda t: t.get("priority_score", 0), reverse=True)
        
        return {"tasks": sorted_tasks}
    
    def _add_timeline_estimates(self, tasks: List[Dict[str, Any]]) -> None:
        """Add timeline estimates to tasks."""
        current_date = datetime.now()
        
        for i, task in enumerate(tasks):
            # Simple timeline calculation (can be made more sophisticated)
            estimated_hours = task.get("estimated_hours", 8)
            days_needed = max(1, estimated_hours // 8)  # Assuming 8 hours per day
            
            start_date = current_date + timedelta(days=i * 2)  # 2 days between task starts
            end_date = start_date + timedelta(days=days_needed)
            
            task["estimated_start_date"] = start_date.isoformat()
            task["estimated_end_date"] = end_date.isoformat()
    
    def _refine_task_estimate(self, task: Dict[str, Any]) -> None:
        """Refine individual task estimate."""
        category = task.get("category", "")
        current_estimate = task.get("estimated_hours", 8)
        
        # Apply category-based adjustments
        multipliers = {
            "setup": 1.2,  # Setup tasks often take longer
            "testing": 1.1,  # Testing can be underestimated
            "deployment": 1.3,  # Deployment often has surprises
            "feature": 1.0,  # Feature development baseline
        }
        
        multiplier = multipliers.get(category, 1.0)
        task["estimated_hours"] = int(current_estimate * multiplier)
    
    def _calculate_priority_score(self, task: Dict[str, Any], all_tasks: List[Dict[str, Any]]) -> float:
        """Calculate priority score for a task."""
        score = 0.0
        
        # Base priority
        priority_scores = {"high": 3.0, "medium": 2.0, "low": 1.0}
        score += priority_scores.get(task.get("priority", "medium"), 2.0)
        
        # Category importance
        category_scores = {"setup": 3.0, "feature": 2.0, "testing": 1.5, "deployment": 1.0}
        score += category_scores.get(task.get("category", "feature"), 2.0)
        
        # Dependency factor (tasks with no dependencies get higher priority)
        dependencies = task.get("dependencies", [])
        if not dependencies:
            score += 1.0
        
        # Effort factor (smaller tasks get slight priority boost)
        estimated_hours = task.get("estimated_hours", 8)
        if estimated_hours <= 4:
            score += 0.5
        
        return score
