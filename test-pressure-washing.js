const axios = require('axios');

const BASE_URL = 'http://localhost:8000/api/v1';

async function testPressureWashingProject() {
    console.log('🧽 Testing Pressure Washing Landing Page Project');
    console.log('===============================================');
    
    try {
        // Create a pressure washing project
        console.log('🔍 Creating pressure washing landing page project...');
        const projectResponse = await axios.post(`${BASE_URL}/projects`, {
            prompt: "A professional pressure washing landing page for a local business. The site should showcase services like driveway cleaning, house washing, deck restoration, and commercial pressure washing. Include before/after photo galleries, service pricing, customer testimonials, contact forms, and online booking functionality. The design should be clean, modern, and mobile-responsive with strong call-to-action buttons."
        });
        
        const projectId = projectResponse.data.project_id;
        console.log(`✅ Project created: ${projectId}`);
        
        // Test all steps in sequence
        const steps = ['analyze', 'clarify', 'summary', 'techstack', 'wireframes', 'tasks'];
        
        for (const step of steps) {
            console.log(`\n🔄 Testing ${step} step...`);
            try {
                const stepResponse = await axios.post(`${BASE_URL}/projects/${projectId}/steps`, {
                    step: step
                });
                
                console.log(`✅ ${step} completed successfully`);
                
                if (step === 'analyze') {
                    console.log(`   Project Type: ${stepResponse.data.result.project_type}`);
                    console.log(`   Complexity: ${stepResponse.data.result.complexity}`);
                    console.log(`   Features: ${stepResponse.data.result.features?.length || 0} features`);
                }
                
                if (step === 'wireframes') {
                    console.log(`   Pages: ${stepResponse.data.result.pages?.length || 0} pages`);
                    if (stepResponse.data.result.pages?.length > 0) {
                        console.log(`   First page: ${stepResponse.data.result.pages[0].name}`);
                    }
                }
                
                if (step === 'tasks') {
                    console.log(`   Total tasks: ${stepResponse.data.result.tasks?.length || 0}`);
                    console.log(`   Total hours: ${stepResponse.data.result.total_estimated_hours || 0}`);
                }
                
            } catch (stepError) {
                console.error(`❌ ${step} step failed:`, stepError.response?.data || stepError.message);
            }
        }
        
        // Get final project status
        console.log('\n📊 Getting final project status...');
        const statusResponse = await axios.get(`${BASE_URL}/projects/${projectId}/status`);
        console.log(`✅ Project status: ${statusResponse.data.status}`);
        console.log(`✅ Progress: ${Math.round(statusResponse.data.progress * 100)}%`);
        
        console.log('\n===============================================');
        console.log('🎉 Pressure washing project test completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
        if (error.response?.data?.detail) {
            console.error('   Detail:', error.response.data.detail);
        }
    }
}

testPressureWashingProject();
