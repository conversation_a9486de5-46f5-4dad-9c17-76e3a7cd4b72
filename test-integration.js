#!/usr/bin/env node
/**
 * Integration test for AG3NT Frontend + Backend
 * 
 * This script tests the complete integration between the Next.js frontend
 * and the Python backend to ensure the refactoring is working correctly.
 */

const https = require('http');

const BACKEND_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3004';

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testBackendHealth() {
  console.log('🔍 Testing backend health...');
  try {
    const response = await makeRequest(`${BACKEND_URL}/health`);
    if (response.status === 200 && response.data.status === 'healthy') {
      console.log('✅ Backend health check passed');
      return true;
    } else {
      console.log('❌ Backend health check failed:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Backend health check error:', error.message);
    return false;
  }
}

async function testProjectCreation() {
  console.log('🔍 Testing project creation...');
  try {
    const projectData = {
      prompt: "Create a simple todo application with React and Node.js",
      is_interactive: false,
      answers: {},
      has_images: false
    };
    
    const response = await makeRequest(`${BACKEND_URL}/api/v1/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(projectData)
    });
    
    if (response.status === 200 && response.data.project_id) {
      console.log('✅ Project creation passed');
      console.log(`   Project ID: ${response.data.project_id}`);
      return response.data.project_id;
    } else {
      console.log('❌ Project creation failed:', response);
      return null;
    }
  } catch (error) {
    console.log('❌ Project creation error:', error.message);
    return null;
  }
}

async function testStepExecution(projectId) {
  console.log('🔍 Testing step execution...');
  try {
    const stepData = {
      step: "analyze",
      context: {
        prompt: "Create a simple todo application with React and Node.js"
      }
    };
    
    const response = await makeRequest(`${BACKEND_URL}/api/v1/projects/${projectId}/steps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(stepData)
    });
    
    if (response.status === 200 && response.data.status === 'completed') {
      console.log('✅ Step execution passed');
      console.log(`   Step: ${response.data.step}`);
      console.log(`   Result: ${JSON.stringify(response.data.result).substring(0, 100)}...`);
      return true;
    } else {
      console.log('❌ Step execution failed:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Step execution error:', error.message);
    return false;
  }
}

async function testProjectStatus(projectId) {
  console.log('🔍 Testing project status...');
  try {
    const response = await makeRequest(`${BACKEND_URL}/api/v1/projects/${projectId}/status`);
    
    if (response.status === 200 && response.data.project_id === projectId) {
      console.log('✅ Project status passed');
      console.log(`   Status: ${response.data.status}`);
      console.log(`   Progress: ${response.data.progress}`);
      return true;
    } else {
      console.log('❌ Project status failed:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Project status error:', error.message);
    return false;
  }
}

async function testFrontendAccess() {
  console.log('🔍 Testing frontend access...');
  try {
    const response = await makeRequest(FRONTEND_URL);
    if (response.status === 200) {
      console.log('✅ Frontend access passed');
      return true;
    } else {
      console.log('❌ Frontend access failed:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Frontend access error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 AG3NT Integration Test Suite');
  console.log('================================');
  console.log();
  
  const results = [];
  
  // Test backend health
  results.push(await testBackendHealth());
  
  // Test frontend access
  results.push(await testFrontendAccess());
  
  // Test project creation
  const projectId = await testProjectCreation();
  results.push(!!projectId);
  
  if (projectId) {
    // Test step execution
    results.push(await testStepExecution(projectId));
    
    // Test project status
    results.push(await testProjectStatus(projectId));
  }
  
  console.log();
  console.log('================================');
  console.log('📊 Test Results');
  console.log('================================');
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log();
    console.log('🎉 All tests passed! AG3NT integration is working correctly.');
    console.log();
    console.log('🔗 Access your application:');
    console.log(`   Frontend: ${FRONTEND_URL}`);
    console.log(`   Backend API: ${BACKEND_URL}/docs`);
    console.log(`   Health Check: ${BACKEND_URL}/health`);
  } else {
    console.log();
    console.log('⚠️  Some tests failed. Please check the services:');
    console.log('   1. Ensure backend is running: python backend/simple_main.py');
    console.log('   2. Ensure frontend is running: pnpm run dev');
    console.log('   3. Check for any error messages above');
  }
  
  process.exit(passed === total ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test suite crashed:', error);
  process.exit(1);
});
