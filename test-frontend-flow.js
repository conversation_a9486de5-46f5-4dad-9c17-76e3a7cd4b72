#!/usr/bin/env node
/**
 * Frontend Flow Test for AG3NT
 * 
 * This script simulates the exact frontend workflow to test the complete flow.
 */

const https = require('http');

const BACKEND_URL = 'http://localhost:8000';

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testCompleteFlow() {
  console.log('🧪 Testing Complete Frontend Flow');
  console.log('==================================');
  console.log();
  
  try {
    // Step 1: Create Project (simulating frontend project creation)
    console.log('📝 Step 1: Creating project...');
    const projectData = {
      prompt: "Create a simple todo application with React and Node.js",
      is_interactive: false,
      answers: {},
      design_style_guide: null,
      has_images: false
    };
    
    const projectResponse = await makeRequest(`${BACKEND_URL}/api/v1/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(projectData)
    });
    
    if (projectResponse.status !== 200) {
      throw new Error(`Project creation failed: ${projectResponse.status}`);
    }
    
    const projectId = projectResponse.data.project_id;
    console.log(`✅ Project created with ID: ${projectId}`);
    console.log();
    
    // Step 2: Execute planning steps (simulating frontend step execution)
    const steps = [
      { step: 'analyze', description: 'Analyze project requirements' },
      { step: 'clarify', description: 'Clarify requirements' },
      { step: 'summary', description: 'Generate project summary' },
      { step: 'techstack', description: 'Select technology stack' },
      { step: 'tasks', description: 'Break down tasks' }
    ];
    
    for (let i = 0; i < steps.length; i++) {
      const { step, description } = steps[i];
      console.log(`🔄 Step ${i + 2}: ${description}...`);
      
      const stepData = {
        step: step,
        context: {
          prompt: projectData.prompt,
          project_id: projectId
        },
        answer: null
      };
      
      const stepResponse = await makeRequest(`${BACKEND_URL}/api/v1/projects/${projectId}/steps`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(stepData)
      });
      
      if (stepResponse.status !== 200) {
        throw new Error(`Step ${step} failed: ${stepResponse.status}`);
      }
      
      console.log(`✅ ${description} completed`);
      console.log(`   Result: ${JSON.stringify(stepResponse.data.result).substring(0, 80)}...`);
      
      // Small delay to simulate real usage
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log();
    
    // Step 3: Check final project status
    console.log('📊 Step 7: Checking final project status...');
    const statusResponse = await makeRequest(`${BACKEND_URL}/api/v1/projects/${projectId}/status`);
    
    if (statusResponse.status !== 200) {
      throw new Error(`Status check failed: ${statusResponse.status}`);
    }
    
    console.log(`✅ Final status: ${statusResponse.data.status}`);
    console.log(`✅ Final progress: ${statusResponse.data.progress * 100}%`);
    console.log(`✅ Completed steps: ${statusResponse.data.completed_steps.length}`);
    
    console.log();
    console.log('==================================');
    console.log('🎉 Complete Frontend Flow Test PASSED!');
    console.log('==================================');
    console.log();
    console.log('✅ All steps completed successfully:');
    console.log('   1. Project creation');
    console.log('   2. Analyze requirements');
    console.log('   3. Clarify requirements');
    console.log('   4. Generate summary');
    console.log('   5. Select tech stack');
    console.log('   6. Break down tasks');
    console.log('   7. Final status check');
    console.log();
    console.log('🚀 The frontend should now work perfectly!');
    console.log('   Open http://localhost:3001 and test the interface.');
    
    return true;
    
  } catch (error) {
    console.error();
    console.error('==================================');
    console.error('❌ Frontend Flow Test FAILED!');
    console.error('==================================');
    console.error();
    console.error('Error:', error.message);
    console.error();
    console.error('🔧 Troubleshooting:');
    console.error('   1. Check that backend is running: python backend/simple_main.py');
    console.error('   2. Check that frontend is running: pnpm run dev');
    console.error('   3. Verify CORS configuration in backend');
    console.error('   4. Check browser console for detailed errors');
    
    return false;
  }
}

// Run the test
testCompleteFlow().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('💥 Test crashed:', error);
  process.exit(1);
});
