"""Context7 client for enhanced context management."""

import httpx
from typing import Any, Dict, List, Optional
from datetime import datetime

from ag3nt_backend.config import settings
from ag3nt_backend.core.logging import LoggerMixin
from ag3nt_backend.core.exceptions import ContextError


class Context7Client(LoggerMixin):
    """Client for interacting with Context7 API."""
    
    def __init__(self):
        self.base_url = settings.context7_base_url
        self.api_key = settings.context7_api_key
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            } if self.api_key else {},
            timeout=30.0,
        )
        
        self.log_info("Context7 client initialized", base_url=self.base_url)
    
    async def search_libraries(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for libraries using Context7."""
        try:
            if not self.api_key:
                self.log_warning("Context7 API key not configured, returning empty results")
                return []
            
            response = await self.client.post(
                "/search/libraries",
                json={
                    "query": query,
                    "limit": limit,
                }
            )
            response.raise_for_status()
            
            results = response.json()
            self.log_info("Library search completed", query=query, results_count=len(results))
            return results
            
        except httpx.HTTPError as e:
            self.log_error("Library search failed", query=query, error=str(e))
            raise ContextError(f"Library search failed: {str(e)}")
    
    async def get_library_docs(
        self,
        library_id: str,
        topic: Optional[str] = None,
        tokens: int = 10000
    ) -> Dict[str, Any]:
        """Get documentation for a specific library."""
        try:
            if not self.api_key:
                self.log_warning("Context7 API key not configured, returning empty docs")
                return {"content": "", "metadata": {}}
            
            params = {
                "library_id": library_id,
                "tokens": tokens,
            }
            if topic:
                params["topic"] = topic
            
            response = await self.client.get("/docs", params=params)
            response.raise_for_status()
            
            docs = response.json()
            self.log_info("Library docs retrieved", library_id=library_id, topic=topic)
            return docs
            
        except httpx.HTTPError as e:
            self.log_error("Library docs retrieval failed", library_id=library_id, error=str(e))
            raise ContextError(f"Library docs retrieval failed: {str(e)}")
    
    async def store_project_context(
        self,
        project_id: str,
        context_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Store project context in Context7."""
        try:
            if not self.api_key:
                self.log_warning("Context7 API key not configured, storing locally")
                return {"stored": False, "message": "API key not configured"}
            
            response = await self.client.post(
                "/context/projects",
                json={
                    "project_id": project_id,
                    "context": context_data,
                    "timestamp": datetime.now().isoformat(),
                }
            )
            response.raise_for_status()
            
            result = response.json()
            self.log_info("Project context stored", project_id=project_id)
            return result
            
        except httpx.HTTPError as e:
            self.log_error("Context storage failed", project_id=project_id, error=str(e))
            raise ContextError(f"Context storage failed: {str(e)}")
    
    async def retrieve_project_context(self, project_id: str) -> Dict[str, Any]:
        """Retrieve project context from Context7."""
        try:
            if not self.api_key:
                self.log_warning("Context7 API key not configured, returning empty context")
                return {}
            
            response = await self.client.get(f"/context/projects/{project_id}")
            response.raise_for_status()
            
            context = response.json()
            self.log_info("Project context retrieved", project_id=project_id)
            return context
            
        except httpx.HTTPError as e:
            if e.response.status_code == 404:
                self.log_info("Project context not found", project_id=project_id)
                return {}
            else:
                self.log_error("Context retrieval failed", project_id=project_id, error=str(e))
                raise ContextError(f"Context retrieval failed: {str(e)}")
    
    async def search_context(
        self,
        query: str,
        project_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Search context data."""
        try:
            if not self.api_key:
                self.log_warning("Context7 API key not configured, returning empty results")
                return []
            
            params = {
                "query": query,
                "limit": limit,
            }
            if project_id:
                params["project_id"] = project_id
            
            response = await self.client.get("/context/search", params=params)
            response.raise_for_status()
            
            results = response.json()
            self.log_info("Context search completed", query=query, results_count=len(results))
            return results
            
        except httpx.HTTPError as e:
            self.log_error("Context search failed", query=query, error=str(e))
            raise ContextError(f"Context search failed: {str(e)}")
    
    async def enhance_with_rag(
        self,
        query: str,
        context: Dict[str, Any],
        project_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Enhance context using RAG (Retrieval-Augmented Generation)."""
        try:
            if not self.api_key:
                self.log_warning("Context7 API key not configured, returning original context")
                return context
            
            response = await self.client.post(
                "/rag/enhance",
                json={
                    "query": query,
                    "context": context,
                    "project_id": project_id,
                }
            )
            response.raise_for_status()
            
            enhanced_context = response.json()
            self.log_info("Context enhanced with RAG", query=query, project_id=project_id)
            return enhanced_context
            
        except httpx.HTTPError as e:
            self.log_error("RAG enhancement failed", query=query, error=str(e))
            # Return original context if enhancement fails
            return context
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
        self.log_info("Context7 client closed")


# Global client instance
_context7_client: Optional[Context7Client] = None


def get_context7_client() -> Context7Client:
    """Get the global Context7 client instance."""
    global _context7_client
    if _context7_client is None:
        _context7_client = Context7Client()
    return _context7_client


async def close_context7_client():
    """Close the global Context7 client."""
    global _context7_client
    if _context7_client is not None:
        await _context7_client.close()
        _context7_client = None
