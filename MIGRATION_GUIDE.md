# AG3NT Migration Guide: Custom Framework → CrewAI + LangGraph

This guide explains how to migrate from the custom AG3NT framework to the new CrewAI + LangGraph architecture.

## Overview

The migration replaces:
- **Custom Planning Engine** → **CrewAI Multi-Agent System**
- **Custom Context Engine** → **Context7 + Enhanced Context Management**
- **Sequential Workflow** → **LangGraph State-Based Workflows**
- **Single AI Service** → **Specialized Agent Crews**

## Architecture Changes

### Before (Custom AG3NT)
```
Next.js Frontend → Custom API Routes → AI Service → Planning Engine → Context Engine
```

### After (CrewAI + LangGraph)
```
Next.js Frontend → Python Backend API → CrewAI Agents → LangGraph Workflows → Context7
```

## Migration Steps

### 1. Set Up Python Backend

```bash
cd backend
pip install poetry
poetry install
cp .env.example .env
# Configure your API keys in .env
```

### 2. Update Environment Variables

Add to your `.env` file:

```bash
# Python Backend URL
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000

# LLM API Keys (same as before)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
OPENROUTER_API_KEY=your_openrouter_key

# New: CrewAI Configuration
CREWAI_API_KEY=your_crewai_key
CREWAI_TELEMETRY_OPT_OUT=true

# New: Context7 Configuration
CONTEXT7_API_KEY=your_context7_key
CONTEXT7_BASE_URL=https://api.context7.com

# Database (for production)
DATABASE_URL=postgresql://user:password@localhost/ag3nt
REDIS_URL=redis://localhost:6379/0
```

### 3. Start Both Services

**Terminal 1 - Python Backend:**
```bash
cd backend
python run.py
```

**Terminal 2 - Next.js Frontend:**
```bash
npm run dev
```

### 4. API Changes

The frontend now uses the Python backend client instead of direct API routes:

#### Before:
```typescript
const response = await fetch("/api/planning/step", {
  method: "POST",
  body: JSON.stringify({ step, context, answer })
})
```

#### After:
```typescript
import { pythonBackendClient } from "@/lib/python-backend-client"

const result = await pythonBackendClient.executeProjectStep(projectId, {
  step,
  context,
  answer
})
```

## Key Benefits

### 1. **Specialized Agents**
- **Project Planning Agent**: Requirements analysis and planning
- **Task Planning Agent**: Task breakdown and estimation
- **Code Generation Agent**: Code and artifact generation
- **Analysis Agent**: Research and competitive analysis
- **Context Management Agent**: Knowledge curation

### 2. **Explicit Workflows**
- **Planning Workflow**: Project initialization and planning
- **Development Workflow**: Code generation and review
- **Research Workflow**: Market analysis and insights

### 3. **Enhanced Context Management**
- **Context7 Integration**: Advanced RAG capabilities
- **Project Memory**: Persistent context across sessions
- **Knowledge Base**: Searchable project knowledge

### 4. **Better Reliability**
- **Error Recovery**: Automatic retry and fallback
- **State Persistence**: Workflow checkpoints
- **Human-in-the-Loop**: Approval gates and feedback

## Compatibility

### Maintained Features
- ✅ All existing UI components work unchanged
- ✅ Same planning steps and workflow
- ✅ Design style guide integration
- ✅ Image upload and analysis
- ✅ Interactive question/answer flow

### Enhanced Features
- 🚀 **Multi-agent collaboration** for better results
- 🚀 **Advanced context management** with Context7
- 🚀 **Explicit workflow control** with LangGraph
- 🚀 **Real-time progress tracking** via WebSockets
- 🚀 **Better error handling** and recovery

### New Capabilities
- 🆕 **Agent performance metrics** and monitoring
- 🆕 **Workflow visualization** and debugging
- 🆕 **Context search** across projects
- 🆕 **Library recommendations** based on project context
- 🆕 **Best practices** suggestions

## Troubleshooting

### Python Backend Not Starting
1. Check Python version (3.11+ required)
2. Verify all dependencies installed: `poetry install`
3. Check environment variables in `.env`
4. Review logs for specific errors

### Frontend Connection Issues
1. Verify `NEXT_PUBLIC_BACKEND_URL` is correct
2. Check Python backend is running on port 8000
3. Review browser console for CORS errors

### Agent/Workflow Errors
1. Check API keys are valid and have credits
2. Review Python backend logs for detailed errors
3. Verify Context7 configuration if using

## Development Workflow

### Adding New Agents
1. Create agent class in `backend/src/ag3nt_backend/agents/`
2. Extend `BaseAG3NTAgent`
3. Implement specialized tools and logic
4. Add to appropriate crew

### Adding New Workflows
1. Create workflow in `backend/src/ag3nt_backend/workflows/`
2. Define state schema and nodes
3. Implement workflow logic with LangGraph
4. Add API endpoints if needed

### Testing
```bash
# Backend tests
cd backend
poetry run pytest

# Frontend tests
npm test
```

## Production Deployment

### Backend (Python)
- Deploy to cloud platform (AWS, GCP, Azure)
- Use PostgreSQL for database
- Use Redis for caching
- Configure proper environment variables

### Frontend (Next.js)
- Deploy to Vercel (recommended)
- Update `NEXT_PUBLIC_BACKEND_URL` to production URL
- Configure environment variables

## Support

For issues or questions:
1. Check the logs in both frontend and backend
2. Review this migration guide
3. Check the README files in each directory
4. Create an issue with detailed error information

The new architecture provides a solid foundation for scaling your agent system while maintaining the excellent user experience of your existing Next.js frontend.
