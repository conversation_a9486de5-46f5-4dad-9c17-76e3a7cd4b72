"""Base agent class for AG3NT agents."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime

from crewai import Agent
from crewai_tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_anthropic import <PERSON>tAnthropic

from ag3nt_backend.config import settings
from ag3nt_backend.core.logging import LoggerMixin
from ag3nt_backend.core.exceptions import Agent<PERSON>rror, LLMError
from ag3nt_backend.models.state import Agent<PERSON><PERSON>, AgentState


class BaseAG3NTAgent(LoggerMixin, ABC):
    """Base class for all AG3NT agents."""
    
    def __init__(
        self,
        role: AgentR<PERSON>,
        goal: str,
        backstory: str,
        tools: Optional[List[BaseTool]] = None,
        llm_model: Optional[str] = None,
        temperature: float = 0.7,
        max_iterations: int = 10,
        verbose: bool = True,
    ):
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.tools = tools or []
        self.llm_model = llm_model or settings.default_llm_model
        self.temperature = temperature
        self.max_iterations = max_iterations
        self.verbose = verbose
        
        # Initialize LLM
        self.llm = self._initialize_llm()
        
        # Create CrewAI agent
        self.agent = self._create_agent()
        
        # Agent state
        self.state = AgentState(
            agent_id=f"{role.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            agent_role=role,
            agent_name=self.__class__.__name__,
            current_task=None,
            task_history=[],
            performance_metrics={},
            last_action=None,
            last_action_timestamp=datetime.now(),
        )
        
        self.log_info(f"Initialized {self.__class__.__name__}", agent_id=self.state["agent_id"])
    
    def _initialize_llm(self):
        """Initialize the LLM based on the model name."""
        try:
            if "gpt" in self.llm_model.lower():
                if not settings.openai_api_key:
                    raise LLMError("OpenAI API key not configured", self.llm_model, "openai")
                return ChatOpenAI(
                    model=self.llm_model,
                    temperature=self.temperature,
                    api_key=settings.openai_api_key,
                )
            elif "claude" in self.llm_model.lower():
                if not settings.anthropic_api_key:
                    raise LLMError("Anthropic API key not configured", self.llm_model, "anthropic")
                return ChatAnthropic(
                    model=self.llm_model,
                    temperature=self.temperature,
                    api_key=settings.anthropic_api_key,
                )
            else:
                # Default to OpenAI
                return ChatOpenAI(
                    model="gpt-4o",
                    temperature=self.temperature,
                    api_key=settings.openai_api_key,
                )
        except Exception as e:
            raise LLMError(f"Failed to initialize LLM: {str(e)}", self.llm_model)
    
    def _create_agent(self) -> Agent:
        """Create the CrewAI agent."""
        try:
            return Agent(
                role=self.role.value.replace("_", " ").title(),
                goal=self.goal,
                backstory=self.backstory,
                tools=self.tools,
                llm=self.llm,
                verbose=self.verbose,
                allow_delegation=True,
                max_iter=self.max_iterations,
            )
        except Exception as e:
            raise AgentError(f"Failed to create agent: {str(e)}", self.state["agent_id"], self.role.value)
    
    @abstractmethod
    def get_specialized_tools(self) -> List[BaseTool]:
        """Get tools specific to this agent type."""
        pass
    
    def update_state(self, **kwargs) -> None:
        """Update agent state."""
        for key, value in kwargs.items():
            if key in self.state:
                self.state[key] = value
        self.state["last_action_timestamp"] = datetime.now()
    
    def add_task_to_history(self, task: str) -> None:
        """Add a task to the agent's history."""
        self.state["task_history"].append(task)
        self.state["current_task"] = task
        self.log_info(f"Added task to history", task=task, agent_id=self.state["agent_id"])
    
    def update_performance_metrics(self, metrics: Dict[str, float]) -> None:
        """Update agent performance metrics."""
        self.state["performance_metrics"].update(metrics)
        self.log_info(f"Updated performance metrics", metrics=metrics, agent_id=self.state["agent_id"])
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get agent information."""
        return {
            "agent_id": self.state["agent_id"],
            "role": self.role.value,
            "name": self.state["agent_name"],
            "goal": self.goal,
            "backstory": self.backstory,
            "current_task": self.state["current_task"],
            "task_count": len(self.state["task_history"]),
            "performance_metrics": self.state["performance_metrics"],
            "last_action": self.state["last_action"],
            "last_action_timestamp": self.state["last_action_timestamp"],
        }
    
    async def execute_task(self, task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a task using this agent."""
        try:
            self.log_info(f"Executing task", task=task_description, agent_id=self.state["agent_id"])
            
            # Update state
            self.add_task_to_history(task_description)
            self.update_state(last_action="executing_task")
            
            # Execute task (to be implemented by subclasses)
            result = await self._execute_task_impl(task_description, context)
            
            # Update state
            self.update_state(last_action="task_completed")
            
            self.log_info(f"Task completed", task=task_description, agent_id=self.state["agent_id"])
            return result
            
        except Exception as e:
            self.log_error(f"Task execution failed", task=task_description, error=str(e), agent_id=self.state["agent_id"])
            self.update_state(last_action="task_failed")
            raise AgentError(f"Task execution failed: {str(e)}", self.state["agent_id"], self.role.value)
    
    @abstractmethod
    async def _execute_task_impl(self, task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Implementation of task execution (to be overridden by subclasses)."""
        pass
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.__class__.__name__}(role={self.role.value}, id={self.state['agent_id']})"
    
    def __repr__(self) -> str:
        """Detailed representation of the agent."""
        return (
            f"{self.__class__.__name__}("
            f"role={self.role.value}, "
            f"id={self.state['agent_id']}, "
            f"current_task={self.state['current_task']}, "
            f"task_count={len(self.state['task_history'])}"
            f")"
        )
