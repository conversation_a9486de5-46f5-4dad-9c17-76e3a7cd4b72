"""Workflow management API endpoints."""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ag3nt_backend.core.logging import get_logger


logger = get_logger(__name__)
router = APIRouter()


class WorkflowResponse(BaseModel):
    """Response model for workflow data."""
    workflow_id: str
    workflow_name: str
    description: str
    status: str
    steps: List[str]
    current_step: str = None
    progress: float = 0.0


@router.get("/", response_model=List[WorkflowResponse])
async def list_workflows() -> List[WorkflowResponse]:
    """List all available workflows."""
    workflows = [
        WorkflowResponse(
            workflow_id="planning_workflow",
            workflow_name="Planning Workflow",
            description="Complete project planning workflow using CrewAI agents",
            status="available",
            steps=[
                "initialize",
                "analyze_project", 
                "clarify_requirements",
                "generate_summary",
                "select_tech_stack",
                "breakdown_tasks",
                "finalize_planning"
            ],
            current_step=None,
            progress=0.0,
        ),
    ]
    
    return workflows


@router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(workflow_id: str) -> WorkflowResponse:
    """Get workflow details by ID."""
    if workflow_id == "planning_workflow":
        return WorkflowResponse(
            workflow_id=workflow_id,
            workflow_name="Planning Workflow",
            description="Complete project planning workflow using CrewAI agents",
            status="available",
            steps=[
                "initialize",
                "analyze_project",
                "clarify_requirements", 
                "generate_summary",
                "select_tech_stack",
                "breakdown_tasks",
                "finalize_planning"
            ],
            current_step=None,
            progress=0.0,
        )
    else:
        raise HTTPException(status_code=404, detail=f"Workflow {workflow_id} not found")


@router.get("/{workflow_id}/status")
async def get_workflow_status(workflow_id: str) -> Dict[str, Any]:
    """Get workflow execution status."""
    return {
        "workflow_id": workflow_id,
        "status": "idle",
        "current_step": None,
        "progress": 0.0,
        "execution_time": 0.0,
        "last_execution": None,
        "error_message": None,
    }
