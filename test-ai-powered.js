const axios = require('axios');

const BASE_URL = 'http://localhost:8000/api/v1';

async function testAIPoweredSystem() {
    console.log('🤖 Testing AI-Powered AG3NT System');
    console.log('=====================================');
    
    try {
        // Create a project
        console.log('🔍 Creating project with zombie game description...');
        const projectResponse = await axios.post(`${BASE_URL}/projects`, {
            prompt: "A unique 3D reverse-zombie game built with Three.js where players control a zombie and spread infection throughout a populated map. The game features an infection chain reaction mechanic, multiple zombie types with special abilities, and power-up systems, creating an engaging strategy-action experience."
        });
        
        const projectId = projectResponse.data.project_id;
        console.log(`✅ Project created: ${projectId}`);
        
        // Test AI-powered analyze step
        console.log('\n🧠 Testing AI-powered analyze step...');
        const analyzeResponse = await axios.post(`${BASE_URL}/projects/${projectId}/steps`, {
            step: 'analyze'
        });
        
        console.log('✅ AI analyze result:');
        console.log(`   Project Type: ${analyzeResponse.data.result.project_type}`);
        console.log(`   Complexity: ${analyzeResponse.data.result.complexity}`);
        console.log(`   Features: ${analyzeResponse.data.result.features?.length || 0} features`);
        console.log(`   Technical Hints: ${analyzeResponse.data.result.technical_hints?.length || 0} hints`);
        console.log(`   Timeline: ${analyzeResponse.data.result.estimated_timeline}`);
        console.log(`   Budget: ${analyzeResponse.data.result.budget_estimate}`);
        
        // Test AI-powered clarify step
        console.log('\n❓ Testing AI-powered clarify step...');
        const clarifyResponse = await axios.post(`${BASE_URL}/projects/${projectId}/steps`, {
            step: 'clarify'
        });
        
        console.log('✅ AI clarify result:');
        console.log(`   Questions: ${clarifyResponse.data.result.questions?.length || 0} questions`);
        if (clarifyResponse.data.result.questions?.length > 0) {
            console.log(`   First question: ${clarifyResponse.data.result.questions[0].question}`);
        }
        
        // Test AI-powered wireframes step
        console.log('\n🎨 Testing AI-powered wireframes step...');
        const wireframesResponse = await axios.post(`${BASE_URL}/projects/${projectId}/steps`, {
            step: 'wireframes'
        });
        
        console.log('✅ AI wireframes result:');
        console.log(`   Pages: ${wireframesResponse.data.result.pages?.length || 0} pages`);
        if (wireframesResponse.data.result.pages?.length > 0) {
            console.log(`   First page: ${wireframesResponse.data.result.pages[0].name}`);
            console.log(`   ASCII wireframe preview:`);
            console.log(wireframesResponse.data.result.pages[0].wireframe?.substring(0, 200) + '...');
        }
        
        // Test AI-powered tasks step
        console.log('\n📋 Testing AI-powered tasks step...');
        const tasksResponse = await axios.post(`${BASE_URL}/projects/${projectId}/steps`, {
            step: 'tasks'
        });
        
        console.log('✅ AI tasks result:');
        console.log(`   Total tasks: ${tasksResponse.data.result.tasks?.length || 0}`);
        console.log(`   Total hours: ${tasksResponse.data.result.total_estimated_hours || 0}`);
        console.log(`   Timeline: ${tasksResponse.data.result.estimated_timeline}`);
        
        if (tasksResponse.data.result.tasks?.length > 0) {
            console.log('\n📋 Sample tasks:');
            tasksResponse.data.result.tasks.slice(0, 3).forEach((task, index) => {
                console.log(`   ${index + 1}. ${task.title} (${task.estimated_hours}h)`);
                console.log(`      Category: ${task.category}, Priority: ${task.priority}`);
                console.log(`      Subtasks: ${task.subtasks?.length || 0} items`);
            });
        }
        
        console.log('\n=====================================');
        console.log('📊 AI-Powered System Test Results');
        console.log('=====================================');
        console.log('✅ AI Analysis: WORKING');
        console.log('✅ AI Clarification: WORKING');
        console.log('✅ AI Wireframes: WORKING');
        console.log('✅ AI Task Generation: WORKING');
        console.log('\n🎉 AI-powered AG3NT system test completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
        if (error.response?.data?.detail) {
            console.error('   Detail:', error.response.data.detail);
        }
    }
}

testAIPoweredSystem();
