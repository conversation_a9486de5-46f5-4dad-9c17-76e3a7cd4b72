"""Enhanced context engine using Context7."""

from typing import Any, Dict, List, Optional
from datetime import datetime

from ag3nt_backend.context.context7_client import get_context7_client
from ag3nt_backend.core.logging import LoggerMixin
from ag3nt_backend.core.exceptions import ContextError


class EnhancedContextEngine(LoggerMixin):
    """Enhanced context engine with Context7 integration."""
    
    def __init__(self):
        self.context7_client = get_context7_client()
        self.local_context_store: Dict[str, Dict[str, Any]] = {}
        
        self.log_info("Enhanced context engine initialized")
    
    async def store_project_context(
        self,
        project_id: str,
        context_data: Dict[str, Any]
    ) -> None:
        """Store project context both locally and in Context7."""
        try:
            # Store locally
            self.local_context_store[project_id] = {
                **context_data,
                "stored_at": datetime.now().isoformat(),
                "version": "1.0.0",
            }
            
            # Store in Context7 (if available)
            try:
                await self.context7_client.store_project_context(project_id, context_data)
            except ContextError as e:
                self.log_warning("Failed to store in Context7, using local storage", error=str(e))
            
            self.log_info("Project context stored", project_id=project_id)
            
        except Exception as e:
            self.log_error("Failed to store project context", project_id=project_id, error=str(e))
            raise ContextError(f"Failed to store project context: {str(e)}")
    
    async def retrieve_project_context(self, project_id: str) -> Dict[str, Any]:
        """Retrieve project context from Context7 or local storage."""
        try:
            # Try Context7 first
            try:
                context = await self.context7_client.retrieve_project_context(project_id)
                if context:
                    self.log_info("Project context retrieved from Context7", project_id=project_id)
                    return context
            except ContextError as e:
                self.log_warning("Failed to retrieve from Context7, trying local", error=str(e))
            
            # Fall back to local storage
            local_context = self.local_context_store.get(project_id, {})
            if local_context:
                self.log_info("Project context retrieved from local storage", project_id=project_id)
            else:
                self.log_info("No project context found", project_id=project_id)
            
            return local_context
            
        except Exception as e:
            self.log_error("Failed to retrieve project context", project_id=project_id, error=str(e))
            return {}
    
    async def enhance_context_with_rag(
        self,
        query: str,
        base_context: Dict[str, Any],
        project_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Enhance context using RAG techniques."""
        try:
            # Use Context7 for RAG enhancement
            enhanced_context = await self.context7_client.enhance_with_rag(
                query=query,
                context=base_context,
                project_id=project_id
            )
            
            # Add local enhancements
            enhanced_context = await self._add_local_enhancements(
                enhanced_context,
                query,
                project_id
            )
            
            self.log_info("Context enhanced with RAG", query=query, project_id=project_id)
            return enhanced_context
            
        except Exception as e:
            self.log_error("RAG enhancement failed", query=query, error=str(e))
            # Return base context if enhancement fails
            return base_context
    
    async def search_relevant_context(
        self,
        query: str,
        project_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Search for relevant context across projects."""
        try:
            # Search in Context7
            try:
                context7_results = await self.context7_client.search_context(
                    query=query,
                    project_id=project_id,
                    limit=limit
                )
            except ContextError:
                context7_results = []
            
            # Search in local storage
            local_results = self._search_local_context(query, project_id, limit)
            
            # Combine and deduplicate results
            all_results = context7_results + local_results
            unique_results = self._deduplicate_results(all_results)
            
            # Sort by relevance (simplified scoring)
            sorted_results = sorted(
                unique_results,
                key=lambda x: x.get("relevance_score", 0.0),
                reverse=True
            )
            
            self.log_info("Context search completed", query=query, results_count=len(sorted_results))
            return sorted_results[:limit]
            
        except Exception as e:
            self.log_error("Context search failed", query=query, error=str(e))
            return []
    
    async def get_library_recommendations(
        self,
        project_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get library recommendations based on project context."""
        try:
            project_type = project_context.get("project_type", "")
            features = project_context.get("features", [])
            tech_stack = project_context.get("tech_stack", {})
            
            # Build search query
            search_terms = [project_type] + features
            if tech_stack.get("frontend"):
                search_terms.append(tech_stack["frontend"])
            if tech_stack.get("backend"):
                search_terms.append(tech_stack["backend"])
            
            query = " ".join(search_terms)
            
            # Search for relevant libraries
            libraries = await self.context7_client.search_libraries(query, limit=20)
            
            # Filter and score libraries
            recommendations = self._score_library_recommendations(libraries, project_context)
            
            self.log_info("Library recommendations generated", count=len(recommendations))
            return recommendations
            
        except Exception as e:
            self.log_error("Failed to get library recommendations", error=str(e))
            return []
    
    async def get_best_practices(
        self,
        project_context: Dict[str, Any],
        category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get best practices for the project."""
        try:
            # Build query for best practices
            project_type = project_context.get("project_type", "")
            domain = project_context.get("domain", "")
            
            query_parts = ["best practices", project_type, domain]
            if category:
                query_parts.append(category)
            
            query = " ".join(filter(None, query_parts))
            
            # Search for best practices
            practices = await self.search_relevant_context(query, limit=15)
            
            # Filter for best practices content
            best_practices = [
                practice for practice in practices
                if "best practice" in practice.get("content", "").lower()
                or "recommendation" in practice.get("content", "").lower()
            ]
            
            self.log_info("Best practices retrieved", category=category, count=len(best_practices))
            return best_practices
            
        except Exception as e:
            self.log_error("Failed to get best practices", category=category, error=str(e))
            return []
    
    def _search_local_context(
        self,
        query: str,
        project_id: Optional[str],
        limit: int
    ) -> List[Dict[str, Any]]:
        """Search local context storage."""
        results = []
        query_lower = query.lower()
        
        for pid, context in self.local_context_store.items():
            if project_id and pid != project_id:
                continue
            
            # Simple text matching (could be improved with proper search)
            context_text = str(context).lower()
            if query_lower in context_text:
                results.append({
                    "project_id": pid,
                    "content": context,
                    "relevance_score": 0.5,  # Simple scoring
                    "source": "local",
                })
        
        return results[:limit]
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results."""
        seen = set()
        unique_results = []
        
        for result in results:
            # Create a simple hash for deduplication
            result_hash = hash(str(result.get("content", "")))
            if result_hash not in seen:
                seen.add(result_hash)
                unique_results.append(result)
        
        return unique_results
    
    def _score_library_recommendations(
        self,
        libraries: List[Dict[str, Any]],
        project_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Score and filter library recommendations."""
        scored_libraries = []
        
        project_features = set(project_context.get("features", []))
        project_type = project_context.get("project_type", "")
        
        for library in libraries:
            score = 0.0
            
            # Score based on feature overlap
            library_tags = set(library.get("tags", []))
            feature_overlap = len(project_features.intersection(library_tags))
            score += feature_overlap * 0.3
            
            # Score based on project type relevance
            if project_type in library.get("description", "").lower():
                score += 0.4
            
            # Score based on popularity/trust
            trust_score = library.get("trust_score", 0)
            score += trust_score * 0.3
            
            if score > 0.2:  # Only include libraries with reasonable scores
                scored_libraries.append({
                    **library,
                    "recommendation_score": score,
                })
        
        # Sort by score
        return sorted(scored_libraries, key=lambda x: x["recommendation_score"], reverse=True)
    
    async def _add_local_enhancements(
        self,
        context: Dict[str, Any],
        query: str,
        project_id: Optional[str]
    ) -> Dict[str, Any]:
        """Add local enhancements to the context."""
        # Add timestamp
        context["enhanced_at"] = datetime.now().isoformat()
        
        # Add query information
        context["enhancement_query"] = query
        
        # Add project-specific enhancements
        if project_id:
            project_context = await self.retrieve_project_context(project_id)
            if project_context:
                context["project_context"] = project_context
        
        return context
