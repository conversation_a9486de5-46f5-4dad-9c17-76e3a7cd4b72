"""Workflow service for managing LangGraph workflows."""

from typing import Any, Dict, Optional
from datetime import datetime

from ag3nt_backend.workflows.planning_workflow import PlanningWorkflow
from ag3nt_backend.services.project_service import ProjectService
from ag3nt_backend.models.state import ProjectStatus
from ag3nt_backend.core.logging import LoggerMixin
from ag3nt_backend.core.exceptions import WorkflowError, NotFoundError


class WorkflowService(LoggerMixin):
    """Service for managing workflows."""
    
    def __init__(self):
        self.project_service = ProjectService()
        self.planning_workflow = PlanningWorkflow()
        self.log_info("Workflow service initialized")
    
    async def start_planning_workflow(
        self,
        project_id: str,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Start the planning workflow for a project."""
        try:
            # Get the project
            project = await self.project_service.get_project(project_id)
            if not project:
                raise NotFoundError(f"Project {project_id} not found", "project")
            
            self.log_info("Starting planning workflow", project_id=project_id)
            
            # Update project status
            await self.project_service.update_project_status(
                project_id,
                ProjectStatus.PLANNING,
                0.1
            )
            
            # Execute the planning workflow
            result = await self.planning_workflow.execute(
                project_id=project_id,
                user_prompt=project["user_prompt"],
                user_answers=project["user_answers"],
                config=config or {}
            )
            
            # Update project with results
            await self._update_project_from_workflow_result(project_id, result)
            
            self.log_info("Planning workflow completed", project_id=project_id)
            return result
            
        except Exception as e:
            self.log_error("Planning workflow failed", project_id=project_id, error=str(e))
            
            # Update project status to failed
            await self.project_service.update_project_status(
                project_id,
                ProjectStatus.FAILED,
                error_message=str(e)
            )
            
            raise WorkflowError(f"Planning workflow failed: {str(e)}", project_id)
    
    async def execute_step(
        self,
        project_id: str,
        step: str,
        context: Dict[str, Any],
        user_answer: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute a specific workflow step."""
        try:
            # Get the project
            project = await self.project_service.get_project(project_id)
            if not project:
                raise NotFoundError(f"Project {project_id} not found", "project")
            
            self.log_info("Executing workflow step", project_id=project_id, step=step)
            
            # Map step to appropriate workflow method
            if step == "analyze":
                result = await self._execute_analyze_step(project, context)
            elif step == "clarify":
                result = await self._execute_clarify_step(project, context, user_answer)
            elif step == "summary":
                result = await self._execute_summary_step(project, context)
            elif step == "techstack":
                result = await self._execute_techstack_step(project, context)
            elif step == "tasks":
                result = await self._execute_tasks_step(project, context)
            else:
                # Generic step execution
                result = await self._execute_generic_step(project, step, context, user_answer)
            
            # Add result to project
            await self.project_service.add_project_result(project_id, step, result)
            
            self.log_info("Workflow step completed", project_id=project_id, step=step)
            return result
            
        except Exception as e:
            self.log_error("Workflow step failed", project_id=project_id, step=step, error=str(e))
            raise WorkflowError(f"Step {step} failed: {str(e)}", project_id, step)
    
    async def _execute_analyze_step(
        self,
        project: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the analyze step."""
        result = await self.planning_workflow.planning_crew.analyze_project(
            project["user_prompt"],
            context
        )
        
        # Update project with analysis results
        await self.project_service.update_project(project["project_id"], {
            "project_type": result.get("project_type", ""),
            "features": result.get("features", []),
            "complexity": result.get("complexity", ""),
            "domain": result.get("domain", ""),
        })
        
        return result
    
    async def _execute_clarify_step(
        self,
        project: Dict[str, Any],
        context: Dict[str, Any],
        user_answer: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute the clarify step."""
        # Get previous analysis
        project_context = project.get("context", {})
        results = project_context.get("results", {})
        analysis = results.get("analyze", {})
        
        # Update user answers if provided
        if user_answer:
            user_answers = project.get("user_answers", {})
            # Simple implementation - in real scenario, you'd map the answer to the right question
            user_answers["latest_answer"] = user_answer
            await self.project_service.update_project(project["project_id"], {
                "user_answers": user_answers
            })
        
        result = await self.planning_workflow.planning_crew.clarify_requirements(
            analysis,
            project.get("user_answers", {})
        )
        
        return result
    
    async def _execute_summary_step(
        self,
        project: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the summary step."""
        # Get previous results
        project_context = project.get("context", {})
        results = project_context.get("results", {})
        analysis = results.get("analyze", {})
        clarifications = results.get("clarify", {})
        
        result = await self.planning_workflow.planning_crew.generate_project_summary(
            analysis,
            clarifications
        )
        
        # Update project summary
        await self.project_service.update_project(project["project_id"], {
            "summary": result
        })
        
        return result
    
    async def _execute_techstack_step(
        self,
        project: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the tech stack selection step."""
        # Simplified tech stack selection
        project_type = project.get("project_type", "")
        
        tech_stack = {
            "frontend": {
                "framework": "React" if "web" in project_type else "React Native",
                "styling": "Tailwind CSS",
                "state_management": "Zustand",
            },
            "backend": {
                "runtime": "Node.js",
                "framework": "Next.js API Routes",
                "database": "PostgreSQL",
                "orm": "Prisma",
            },
            "deployment": {
                "platform": "Vercel",
                "database_hosting": "Supabase",
                "cdn": "Vercel Edge Network",
            },
            "tools": {
                "version_control": "Git",
                "package_manager": "pnpm",
                "testing": "Jest + React Testing Library",
                "linting": "ESLint + Prettier",
            }
        }
        
        # Update project tech stack
        await self.project_service.update_project(project["project_id"], {
            "tech_stack": tech_stack
        })
        
        return tech_stack
    
    async def _execute_tasks_step(
        self,
        project: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the task breakdown step."""
        summary = project.get("summary", {})
        tech_stack = project.get("tech_stack", {})
        
        result = await self.planning_workflow.planning_crew.breakdown_tasks(
            summary,
            tech_stack
        )
        
        # Update project tasks
        await self.project_service.update_project(project["project_id"], {
            "tasks": result.get("tasks", [])
        })
        
        return result
    
    async def _execute_generic_step(
        self,
        project: Dict[str, Any],
        step: str,
        context: Dict[str, Any],
        user_answer: Optional[str] = None
    ) -> Dict[str, Any]:
        """Execute a generic step (fallback)."""
        self.log_warning("Executing generic step", step=step, project_id=project["project_id"])
        
        # Return a basic result
        return {
            "step": step,
            "status": "completed",
            "message": f"Step {step} executed successfully",
            "timestamp": datetime.now().isoformat(),
        }
    
    async def _update_project_from_workflow_result(
        self,
        project_id: str,
        workflow_result: Dict[str, Any]
    ) -> None:
        """Update project with workflow results."""
        updates = {}
        
        # Extract key information from workflow result
        if "analysis" in workflow_result:
            analysis = workflow_result["analysis"]
            updates.update({
                "project_type": analysis.get("project_type", ""),
                "features": analysis.get("features", []),
                "complexity": analysis.get("complexity", ""),
                "domain": analysis.get("domain", ""),
            })
        
        if "summary" in workflow_result:
            updates["summary"] = workflow_result["summary"]
        
        if "tech_stack" in workflow_result:
            updates["tech_stack"] = workflow_result["tech_stack"]
        
        if "tasks" in workflow_result:
            task_data = workflow_result["tasks"]
            updates["tasks"] = task_data.get("tasks", [])
        
        # Update status and progress
        if workflow_result.get("status"):
            updates["status"] = workflow_result["status"]
        
        if workflow_result.get("progress") is not None:
            updates["progress"] = workflow_result["progress"]
        
        # Apply updates
        if updates:
            await self.project_service.update_project(project_id, updates)
    
    def get_service_info(self) -> Dict[str, Any]:
        """Get workflow service information."""
        return {
            "service_name": "Workflow Service",
            "available_workflows": [
                "planning_workflow",
            ],
            "available_steps": [
                "analyze",
                "clarify", 
                "summary",
                "techstack",
                "tasks",
            ],
        }
