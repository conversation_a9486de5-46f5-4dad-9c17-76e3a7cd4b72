<!DOCTYPE html>
<html>
<head>
    <title>Frontend Client Test</title>
</head>
<body>
    <h1>Frontend Client Test</h1>
    <button onclick="testDirectFetch()">Test Direct Fetch</button>
    <button onclick="testPythonClient()">Test Python Client</button>
    <div id="result"></div>

    <script type="module">
        // Test direct fetch
        window.testDirectFetch = async function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing direct fetch...';
            
            try {
                console.log('🧪 Testing direct fetch to backend...');
                
                const projectData = {
                    prompt: "Test project from direct fetch",
                    is_interactive: false,
                    answers: {},
                    has_images: false
                };
                
                console.log('📝 Request data:', projectData);
                console.log('🌐 Backend URL:', 'http://localhost:8000');
                
                const response = await fetch('http://localhost:8000/api/v1/projects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(projectData)
                });
                
                console.log('📊 Response status:', response.status);
                console.log('📊 Response headers:', [...response.headers.entries()]);
                
                const result = await response.json();
                console.log('✅ Response data:', result);
                console.log('🔍 Project ID:', result?.project_id);
                
                resultDiv.innerHTML = `
                    <h3>✅ Direct Fetch Success!</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Project ID:</strong> ${result.project_id}</p>
                    <p><strong>Status:</strong> ${result.status}</p>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('❌ Direct fetch error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Direct Fetch Error!</h3>
                    <p>${error.message}</p>
                    <pre>${error.stack}</pre>
                `;
            }
        };

        // Test Python client (would need to import the actual client)
        window.testPythonClient = async function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Python client test not implemented in this simple test page.';
        };
    </script>
</body>
</html>
