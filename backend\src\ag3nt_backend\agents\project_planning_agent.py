"""Project Planning Agent implementation."""

from typing import Any, Dict, List
import json

from crewai_tools import <PERSON>Tool, SerperDevTool, WebsiteSearchTool
from pydantic import BaseModel, Field

from ag3nt_backend.agents.base_agent import BaseAG3NTAgent
from ag3nt_backend.models.state import <PERSON><PERSON><PERSON>
from ag3nt_backend.core.exceptions import AgentError


class ProjectAnalysisTool(BaseTool):
    """Tool for analyzing project requirements."""
    
    name: str = "project_analysis_tool"
    description: str = "Analyze project requirements and extract key features"
    
    def _run(self, prompt: str) -> str:
        """Analyze the project prompt and extract structured information."""
        # This would integrate with your existing analysis logic
        # For now, return a structured analysis
        analysis = {
            "project_type": self._detect_project_type(prompt),
            "features": self._extract_features(prompt),
            "complexity": self._assess_complexity(prompt),
            "domain": self._detect_domain(prompt),
            "technical_hints": self._get_technical_hints(prompt),
        }
        return json.dumps(analysis)
    
    def _detect_project_type(self, prompt: str) -> str:
        """Detect the type of project from the prompt."""
        prompt_lower = prompt.lower()
        if any(word in prompt_lower for word in ["web app", "website", "web application"]):
            return "web_application"
        elif any(word in prompt_lower for word in ["mobile app", "ios", "android"]):
            return "mobile_application"
        elif any(word in prompt_lower for word in ["api", "backend", "server"]):
            return "backend_service"
        elif any(word in prompt_lower for word in ["dashboard", "admin", "management"]):
            return "dashboard"
        else:
            return "general_application"
    
    def _extract_features(self, prompt: str) -> List[str]:
        """Extract key features from the prompt."""
        features = []
        prompt_lower = prompt.lower()
        
        # Common features
        if "auth" in prompt_lower or "login" in prompt_lower:
            features.append("authentication")
        if "database" in prompt_lower or "data" in prompt_lower:
            features.append("data_management")
        if "payment" in prompt_lower or "billing" in prompt_lower:
            features.append("payment_processing")
        if "notification" in prompt_lower or "email" in prompt_lower:
            features.append("notifications")
        if "search" in prompt_lower:
            features.append("search_functionality")
        if "upload" in prompt_lower or "file" in prompt_lower:
            features.append("file_management")
        
        return features
    
    def _assess_complexity(self, prompt: str) -> str:
        """Assess project complexity."""
        prompt_lower = prompt.lower()
        complexity_indicators = 0
        
        # Count complexity indicators
        if any(word in prompt_lower for word in ["integration", "api", "third-party"]):
            complexity_indicators += 1
        if any(word in prompt_lower for word in ["real-time", "websocket", "live"]):
            complexity_indicators += 1
        if any(word in prompt_lower for word in ["machine learning", "ai", "analytics"]):
            complexity_indicators += 2
        if any(word in prompt_lower for word in ["microservices", "distributed", "scalable"]):
            complexity_indicators += 2
        
        if complexity_indicators >= 3:
            return "high"
        elif complexity_indicators >= 1:
            return "medium"
        else:
            return "low"
    
    def _detect_domain(self, prompt: str) -> str:
        """Detect the business domain."""
        prompt_lower = prompt.lower()
        
        if any(word in prompt_lower for word in ["ecommerce", "shop", "store", "marketplace"]):
            return "ecommerce"
        elif any(word in prompt_lower for word in ["social", "community", "chat", "messaging"]):
            return "social"
        elif any(word in prompt_lower for word in ["finance", "banking", "payment", "fintech"]):
            return "finance"
        elif any(word in prompt_lower for word in ["health", "medical", "healthcare"]):
            return "healthcare"
        elif any(word in prompt_lower for word in ["education", "learning", "course", "school"]):
            return "education"
        else:
            return "general"
    
    def _get_technical_hints(self, prompt: str) -> List[str]:
        """Get technical implementation hints."""
        hints = []
        prompt_lower = prompt.lower()
        
        if "react" in prompt_lower:
            hints.append("Consider React for frontend")
        if "node" in prompt_lower or "javascript" in prompt_lower:
            hints.append("Consider Node.js for backend")
        if "python" in prompt_lower:
            hints.append("Consider Python for backend")
        if "database" in prompt_lower:
            hints.append("Database design will be important")
        if "mobile" in prompt_lower:
            hints.append("Consider responsive design or native mobile")
        
        return hints


class RequirementsClarificationTool(BaseTool):
    """Tool for generating clarification questions."""
    
    name: str = "requirements_clarification_tool"
    description: str = "Generate clarification questions for project requirements"
    
    def _run(self, analysis: str, existing_answers: str = "{}") -> str:
        """Generate clarification questions based on analysis."""
        try:
            analysis_data = json.loads(analysis)
            existing_data = json.loads(existing_answers)
        except json.JSONDecodeError:
            return json.dumps({"questions": []})
        
        questions = []
        
        # Generate questions based on project type
        project_type = analysis_data.get("project_type", "")
        if project_type == "web_application" and "target_audience" not in existing_data:
            questions.append({
                "id": "target_audience",
                "question": "Who is your target audience for this web application?",
                "type": "text",
                "optional": False
            })
        
        if "authentication" in analysis_data.get("features", []) and "auth_method" not in existing_data:
            questions.append({
                "id": "auth_method",
                "question": "What authentication method would you prefer? (email/password, social login, etc.)",
                "type": "text",
                "optional": True
            })
        
        # Add more questions based on complexity and features
        if analysis_data.get("complexity") == "high" and "timeline" not in existing_data:
            questions.append({
                "id": "timeline",
                "question": "What is your preferred timeline for this project?",
                "type": "text",
                "optional": True
            })
        
        return json.dumps({"questions": questions})


class ProjectPlanningAgent(BaseAG3NTAgent):
    """Agent responsible for project planning and analysis."""
    
    def __init__(self):
        super().__init__(
            role=AgentRole.PROJECT_PLANNER,
            goal="Analyze project requirements and create comprehensive project plans",
            backstory="""You are a senior project analyst with over 10 years of experience 
            in software development and project management. You excel at understanding 
            user requirements, identifying potential challenges, and creating detailed 
            project plans that set teams up for success.""",
            tools=self.get_specialized_tools(),
        )
    
    def get_specialized_tools(self) -> List[BaseTool]:
        """Get tools specific to project planning."""
        return [
            ProjectAnalysisTool(),
            RequirementsClarificationTool(),
            SerperDevTool(),  # For market research
            WebsiteSearchTool(),  # For competitive analysis
        ]
    
    async def _execute_task_impl(self, task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute project planning task."""
        try:
            task_type = context.get("task_type", "analyze")
            
            if task_type == "analyze":
                return await self._analyze_project(context)
            elif task_type == "clarify":
                return await self._clarify_requirements(context)
            elif task_type == "summarize":
                return await self._generate_summary(context)
            else:
                raise AgentError(f"Unknown task type: {task_type}")
                
        except Exception as e:
            self.log_error(f"Project planning task failed", task_type=task_type, error=str(e))
            raise
    
    async def _analyze_project(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze project requirements."""
        prompt = context.get("prompt", "")
        if not prompt:
            raise AgentError("No prompt provided for analysis")
        
        self.log_info("Analyzing project requirements", prompt_length=len(prompt))
        
        # Use the analysis tool
        analysis_tool = ProjectAnalysisTool()
        analysis_result = analysis_tool._run(prompt)
        
        try:
            analysis = json.loads(analysis_result)
            self.log_info("Project analysis completed", project_type=analysis.get("project_type"))
            return analysis
        except json.JSONDecodeError:
            raise AgentError("Failed to parse analysis result")
    
    async def _clarify_requirements(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate clarification questions."""
        analysis = context.get("analysis", {})
        existing_answers = context.get("user_answers", {})
        
        self.log_info("Generating clarification questions")
        
        # Use the clarification tool
        clarification_tool = RequirementsClarificationTool()
        questions_result = clarification_tool._run(
            json.dumps(analysis),
            json.dumps(existing_answers)
        )
        
        try:
            questions_data = json.loads(questions_result)
            self.log_info("Clarification questions generated", count=len(questions_data.get("questions", [])))
            return questions_data
        except json.JSONDecodeError:
            raise AgentError("Failed to parse clarification questions")
    
    async def _generate_summary(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate project summary."""
        analysis = context.get("analysis", {})
        clarifications = context.get("clarifications", {})
        
        self.log_info("Generating project summary")
        
        # Create comprehensive summary
        summary = {
            "project_overview": self._create_project_overview(analysis, clarifications),
            "key_features": analysis.get("features", []),
            "technical_requirements": self._extract_technical_requirements(analysis, clarifications),
            "project_scope": self._define_project_scope(analysis, clarifications),
            "success_criteria": self._define_success_criteria(analysis, clarifications),
            "risks_and_challenges": self._identify_risks(analysis),
            "recommendations": self._generate_recommendations(analysis, clarifications),
        }
        
        self.log_info("Project summary generated")
        return summary
    
    def _create_project_overview(self, analysis: Dict[str, Any], clarifications: Dict[str, Any]) -> str:
        """Create a project overview."""
        project_type = analysis.get("project_type", "application")
        domain = analysis.get("domain", "general")
        complexity = analysis.get("complexity", "medium")
        
        return f"This is a {complexity} complexity {project_type} in the {domain} domain."
    
    def _extract_technical_requirements(self, analysis: Dict[str, Any], clarifications: Dict[str, Any]) -> List[str]:
        """Extract technical requirements."""
        requirements = []
        
        # Add requirements based on features
        features = analysis.get("features", [])
        if "authentication" in features:
            requirements.append("User authentication system")
        if "data_management" in features:
            requirements.append("Database design and management")
        if "payment_processing" in features:
            requirements.append("Payment gateway integration")
        
        return requirements
    
    def _define_project_scope(self, analysis: Dict[str, Any], clarifications: Dict[str, Any]) -> Dict[str, Any]:
        """Define project scope."""
        return {
            "in_scope": analysis.get("features", []),
            "out_of_scope": ["Advanced analytics", "Machine learning features"],
            "assumptions": ["Standard web technologies", "Cloud deployment"],
        }
    
    def _define_success_criteria(self, analysis: Dict[str, Any], clarifications: Dict[str, Any]) -> List[str]:
        """Define success criteria."""
        return [
            "Application meets all functional requirements",
            "Performance meets industry standards",
            "Security best practices implemented",
            "User experience is intuitive and responsive",
        ]
    
    def _identify_risks(self, analysis: Dict[str, Any]) -> List[Dict[str, str]]:
        """Identify project risks."""
        risks = []
        
        complexity = analysis.get("complexity", "medium")
        if complexity == "high":
            risks.append({
                "risk": "Technical complexity may lead to delays",
                "mitigation": "Break down into smaller phases",
                "impact": "medium"
            })
        
        return risks
    
    def _generate_recommendations(self, analysis: Dict[str, Any], clarifications: Dict[str, Any]) -> List[str]:
        """Generate project recommendations."""
        recommendations = []
        
        # Add recommendations based on technical hints
        hints = analysis.get("technical_hints", [])
        recommendations.extend(hints)
        
        # Add general recommendations
        recommendations.append("Start with MVP and iterate")
        recommendations.append("Implement comprehensive testing strategy")
        recommendations.append("Plan for scalability from the beginning")
        
        return recommendations
