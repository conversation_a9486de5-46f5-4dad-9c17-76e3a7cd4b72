"""Project management API endpoints."""

from typing import Dict, Any, Optional
from uuid import uuid4
from datetime import datetime

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from ag3nt_backend.core.exceptions import NotFoundError, ValidationError
from ag3nt_backend.core.logging import get_logger
from ag3nt_backend.models.state import ProjectStatus
from ag3nt_backend.services.project_service import ProjectService
from ag3nt_backend.services.workflow_service import WorkflowService


logger = get_logger(__name__)
router = APIRouter()

# Initialize services (will be properly injected later)
# Note: In production, these would be injected via dependency injection
project_service = None
workflow_service = None

def get_project_service():
    global project_service
    if project_service is None:
        project_service = ProjectService()
    return project_service

def get_workflow_service():
    global workflow_service
    if workflow_service is None:
        workflow_service = WorkflowService()
    return workflow_service


# Request/Response models
class ProjectCreateRequest(BaseModel):
    """Request model for creating a new project."""
    prompt: str = Field(..., description="User's project description")
    is_interactive: bool = Field(default=True, description="Enable interactive mode")
    answers: Dict[str, Any] = Field(default_factory=dict, description="User answers")
    design_style_guide: Optional[Dict[str, Any]] = Field(default=None, description="Design style guide")
    has_images: bool = Field(default=False, description="Whether images were uploaded")


class ProjectResponse(BaseModel):
    """Response model for project data."""
    project_id: str
    status: ProjectStatus
    progress: float
    user_prompt: str
    created_at: datetime
    updated_at: datetime
    results: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None


class ProjectStepRequest(BaseModel):
    """Request model for executing a project step."""
    step: str = Field(..., description="Step name to execute")
    context: Dict[str, Any] = Field(default_factory=dict, description="Step context")
    answer: Optional[str] = Field(default=None, description="User answer for interactive steps")


class ProjectStepResponse(BaseModel):
    """Response model for project step execution."""
    project_id: str
    step: str
    status: str
    result: Dict[str, Any]
    needs_input: bool = False
    question: Optional[str] = None
    next_step: Optional[str] = None


@router.post("/", response_model=ProjectResponse)
async def create_project(
    request: ProjectCreateRequest,
    background_tasks: BackgroundTasks
) -> ProjectResponse:
    """
    Create a new project and start the planning workflow.
    
    This endpoint replaces the original /api/planning route.
    """
    try:
        logger.info("Creating new project", prompt=request.prompt[:100])
        
        # Create project
        project = await get_project_service().create_project(
            user_prompt=request.prompt,
            is_interactive=request.is_interactive,
            user_answers=request.answers,
            design_style_guide=request.design_style_guide,
            has_images=request.has_images,
        )
        
        # Start planning workflow in background
        background_tasks.add_task(
            get_workflow_service().start_planning_workflow,
            project["project_id"]
        )
        
        return ProjectResponse(
            project_id=project["project_id"],
            status=project["status"],
            progress=project["progress"],
            user_prompt=project["user_prompt"],
            created_at=project["created_at"],
            updated_at=project["updated_at"],
            results=project["context"].get("results", {}),
        )
        
    except Exception as e:
        logger.error("Failed to create project", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(project_id: str) -> ProjectResponse:
    """Get project details by ID."""
    try:
        project = await get_project_service().get_project(project_id)
        if not project:
            raise NotFoundError(f"Project {project_id} not found", "project")
        
        return ProjectResponse(
            project_id=project["project_id"],
            status=project["status"],
            progress=project["progress"],
            user_prompt=project["user_prompt"],
            created_at=project["created_at"],
            updated_at=project["updated_at"],
            results=project["context"].get("results", {}),
            error_message=project["error_message"],
        )
        
    except NotFoundError:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    except Exception as e:
        logger.error("Failed to get project", project_id=project_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{project_id}/steps", response_model=ProjectStepResponse)
async def execute_project_step(
    project_id: str,
    request: ProjectStepRequest,
    background_tasks: BackgroundTasks
) -> ProjectStepResponse:
    """
    Execute a specific project step.
    
    This endpoint replaces the original /api/planning/step route.
    """
    try:
        logger.info(
            "Executing project step",
            project_id=project_id,
            step=request.step
        )
        
        # Get project
        project = await get_project_service().get_project(project_id)
        if not project:
            raise NotFoundError(f"Project {project_id} not found", "project")

        # Execute step using workflow service
        result = await get_workflow_service().execute_step(
            project_id=project_id,
            step=request.step,
            context=request.context,
            user_answer=request.answer,
        )
        
        return ProjectStepResponse(
            project_id=project_id,
            step=request.step,
            status="completed",
            result=result,
            needs_input=result.get("needs_input", False),
            question=result.get("question"),
            next_step=result.get("next_step"),
        )
        
    except NotFoundError:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        logger.error(
            "Failed to execute project step",
            project_id=project_id,
            step=request.step,
            error=str(e)
        )
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/status")
async def get_project_status(project_id: str) -> Dict[str, Any]:
    """Get project status and progress."""
    try:
        project = await get_project_service().get_project(project_id)
        if not project:
            raise NotFoundError(f"Project {project_id} not found", "project")
        
        return {
            "project_id": project_id,
            "status": project["status"],
            "progress": project["progress"],
            "current_step": project["context"].get("current_step"),
            "completed_steps": project["context"].get("completed_steps", []),
            "error_message": project["error_message"],
            "updated_at": project["updated_at"],
        }
        
    except NotFoundError:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    except Exception as e:
        logger.error("Failed to get project status", project_id=project_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{project_id}")
async def delete_project(project_id: str) -> Dict[str, str]:
    """Delete a project."""
    try:
        success = await get_project_service().delete_project(project_id)
        if not success:
            raise NotFoundError(f"Project {project_id} not found", "project")
        
        return {"message": f"Project {project_id} deleted successfully"}
        
    except NotFoundError:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    except Exception as e:
        logger.error("Failed to delete project", project_id=project_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
