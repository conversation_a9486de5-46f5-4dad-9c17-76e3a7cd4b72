#!/usr/bin/env node

const BACKEND_URL = 'http://localhost:8000';

async function testEnhancedContent() {
    console.log('🧪 Testing Enhanced Content Generation');
    console.log('=====================================');
    
    try {
        // Create project with detailed zombie game description
        console.log('🔍 Creating project with detailed description...');
        const createResponse = await fetch(`${BACKEND_URL}/api/v1/projects`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: "A unique 3D reverse-zombie game built with Three.js where players control a zombie and spread infection throughout a populated map. The game features an infection chain reaction mechanic, multiple zombie types with special abilities, and power-up systems, creating an engaging strategy-action experience. Scope: A single-player 3D game featuring multiple maps with varying difficulty levels. Players control a primary zombie character, strategically infecting humans who then become AI-controlled zombie allies. The game includes a power-up system, multiple zombie mutations with unique abilities, and progressive difficulty scaling based on infection count."
            })
        });
        
        if (!createResponse.ok) {
            throw new Error(`Project creation failed: ${createResponse.status}`);
        }
        
        const project = await createResponse.json();
        console.log(`✅ Project created: ${project.project_id}`);
        
        // Test analyze step with enhanced content
        console.log('\n🔍 Testing enhanced analyze step...');
        const analyzeResponse = await fetch(`${BACKEND_URL}/api/v1/projects/${project.project_id}/steps`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                step: 'analyze'
            })
        });
        
        if (!analyzeResponse.ok) {
            throw new Error(`Analyze step failed: ${analyzeResponse.status}`);
        }
        
        const analyzeResult = await analyzeResponse.json();
        console.log('✅ Enhanced analyze result:');
        console.log('   Raw result:', JSON.stringify(analyzeResult, null, 2));

        // Handle the response structure
        const result = analyzeResult.result || analyzeResult;
        console.log(`   Project Type: ${result.project_type}`);
        console.log(`   Features: ${result.features ? result.features.join(', ') : 'none'}`);
        console.log(`   Complexity: ${result.complexity}`);
        console.log(`   Domain: ${result.domain}`);
        console.log(`   Technical Hints: ${result.technical_hints ? result.technical_hints.length : 0} hints`);
        
        // Test clarify step
        console.log('\n🔍 Testing clarify step...');
        const clarifyResponse = await fetch(`${BACKEND_URL}/api/v1/projects/${project.project_id}/steps`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                step: 'clarify'
            })
        });
        
        const clarifyResult = await clarifyResponse.json();
        console.log('✅ Clarify result:');
        const clarifyData = clarifyResult.result || clarifyResult;
        console.log(`   Questions: ${clarifyData.questions ? clarifyData.questions.length : 0} questions`);
        if (clarifyData.questions && clarifyData.questions.length > 0) {
            console.log(`   First question: ${clarifyData.questions[0].question}`);
        }
        
        // Test summary step
        console.log('\n🔍 Testing summary step...');
        const summaryResponse = await fetch(`${BACKEND_URL}/api/v1/projects/${project.project_id}/steps`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                step: 'summary'
            })
        });
        
        const summaryResult = await summaryResponse.json();
        console.log('✅ Summary result:');
        const summaryData = summaryResult.result || summaryResult;
        console.log(`   Overview length: ${summaryData.project_overview ? summaryData.project_overview.length : 0} characters`);
        console.log(`   Key features: ${summaryData.key_features ? summaryData.key_features.length : 0} features`);
        console.log(`   Technical requirements: ${summaryData.technical_requirements ? summaryData.technical_requirements.length : 0} requirements`);
        
        // Test techstack step
        console.log('\n🔍 Testing techstack step...');
        const techstackResponse = await fetch(`${BACKEND_URL}/api/v1/projects/${project.project_id}/steps`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                step: 'techstack'
            })
        });
        
        const techstackResult = await techstackResponse.json();
        console.log('✅ Techstack result:');
        const techstackData = techstackResult.result || techstackResult;
        console.log(`   Frontend: ${techstackData.frontend ? techstackData.frontend.primary : 'N/A'}`);
        console.log(`   Backend: ${techstackData.backend ? techstackData.backend.primary : 'N/A'}`);
        console.log(`   Database: ${techstackData.database ? techstackData.database.primary : 'N/A'}`);
        console.log(`   Architecture: ${techstackData.architecture_pattern || 'N/A'}`);
        
        // Test tasks step with detailed breakdown
        console.log('\n🔍 Testing detailed tasks step...');
        const tasksResponse = await fetch(`${BACKEND_URL}/api/v1/projects/${project.project_id}/steps`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                step: 'tasks'
            })
        });
        
        const tasksResult = await tasksResponse.json();
        console.log('✅ Detailed tasks result:');
        const tasksData = tasksResult.result || tasksResult;
        console.log(`   Total tasks: ${tasksData.tasks ? tasksData.tasks.length : 0}`);
        console.log(`   Total hours: ${tasksData.total_estimated_hours || 0}`);
        console.log(`   Timeline: ${tasksData.estimated_timeline || 'N/A'}`);
        console.log(`   Team size: ${tasksData.team_size_recommendation || 'N/A'}`);

        if (tasksData.tasks && tasksData.tasks.length > 0) {
            console.log('\n📋 Sample tasks:');
            tasksData.tasks.slice(0, 3).forEach((task, index) => {
                console.log(`   ${index + 1}. ${task.title} (${task.estimated_hours}h)`);
                console.log(`      Category: ${task.category}, Priority: ${task.priority}`);
                if (task.subtasks && task.subtasks.length > 0) {
                    console.log(`      Subtasks: ${task.subtasks.length} items`);
                }
            });
        }
        
        // Get final project status
        console.log('\n🔍 Getting final project status...');
        const statusResponse = await fetch(`${BACKEND_URL}/api/v1/projects/${project.project_id}/status`);
        const status = await statusResponse.json();
        
        console.log('\n=====================================');
        console.log('📊 Enhanced Content Generation Results');
        console.log('=====================================');
        console.log(`✅ Project Type Detection: ${result.project_type === 'game_application' ? 'CORRECT' : 'INCORRECT'}`);
        console.log(`✅ Feature Extraction: ${result.features && result.features.includes('3d_graphics') ? 'CORRECT' : 'INCORRECT'}`);
        console.log(`✅ Complexity Assessment: ${result.complexity === 'high' ? 'CORRECT' : 'INCORRECT'}`);
        console.log(`✅ Clarification Questions: ${clarifyData.questions && clarifyData.questions.length > 0 ? 'GENERATED' : 'NONE'}`);
        console.log(`✅ Detailed Summary: ${summaryData.project_overview && summaryData.project_overview.length > 500 ? 'COMPREHENSIVE' : 'BASIC'}`);
        console.log(`✅ Game-Specific Tech Stack: ${techstackData.frontend && techstackData.frontend.primary === 'Three.js' ? 'CORRECT' : 'GENERIC'}`);
        console.log(`✅ Detailed Task Breakdown: ${tasksData.tasks && tasksData.tasks.length > 5 ? 'COMPREHENSIVE' : 'BASIC'}`);
        console.log(`✅ Project Progress: ${status.progress * 100}%`);
        
        console.log('\n🎉 Enhanced content generation test completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

testEnhancedContent();
