#!/bin/bash

# AG3NT Development Startup Script
# This script starts both the Python backend and Next.js frontend

echo "🚀 Starting AG3NT Development Environment"
echo "=========================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.11 or higher."
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check if Poetry is available
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry for Python dependency management."
    echo "   Visit: https://python-poetry.org/docs/#installation"
    exit 1
fi

# Function to check if port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  Port $1 is already in use. Please stop the service using this port."
        return 1
    fi
    return 0
}

# Check if required ports are available
echo "🔍 Checking port availability..."
if ! check_port 3000; then
    echo "   Frontend port 3000 is in use"
    exit 1
fi

if ! check_port 8000; then
    echo "   Backend port 8000 is in use"
    exit 1
fi

echo "✅ Ports 3000 and 8000 are available"

# Check if .env files exist
echo "🔍 Checking environment configuration..."
if [ ! -f ".env" ]; then
    echo "⚠️  Frontend .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "   Please configure your API keys in .env"
fi

if [ ! -f "backend/.env" ]; then
    echo "⚠️  Backend .env file not found. Copying from backend/.env.example..."
    cp backend/.env.example backend/.env
    echo "   Please configure your API keys in backend/.env"
fi

echo "✅ Environment files are ready"

# Install dependencies if needed
echo "📦 Checking dependencies..."

# Check frontend dependencies
if [ ! -d "node_modules" ]; then
    echo "   Installing frontend dependencies..."
    npm install
fi

# Check backend dependencies
cd backend
if [ ! -d ".venv" ] && [ ! -f "poetry.lock" ]; then
    echo "   Installing backend dependencies..."
    poetry install
fi
cd ..

echo "✅ Dependencies are ready"

# Create log directory
mkdir -p logs

echo ""
echo "🎯 Starting services..."
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   Backend Docs: http://localhost:8000/docs"
echo ""
echo "📝 Logs will be saved to:"
echo "   Frontend: logs/frontend.log"
echo "   Backend: logs/backend.log"
echo ""
echo "🛑 Press Ctrl+C to stop all services"
echo ""

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo "✅ All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend
echo "🐍 Starting Python backend..."
cd backend
poetry run python run.py > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Check if backend started successfully
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "❌ Backend failed to start. Check logs/backend.log for details."
    exit 1
fi

# Test backend health
echo "🔍 Testing backend connection..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend is running and healthy"
else
    echo "⚠️  Backend is starting but not yet responding. This is normal."
fi

# Start frontend
echo "⚛️  Starting Next.js frontend..."
npm run dev > logs/frontend.log 2>&1 &
FRONTEND_PID=$!

# Wait a moment for frontend to start
sleep 3

# Check if frontend started successfully
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    echo "❌ Frontend failed to start. Check logs/frontend.log for details."
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo "✅ Frontend is starting..."
echo ""
echo "🎉 AG3NT Development Environment is ready!"
echo ""
echo "📱 Open your browser and navigate to: http://localhost:3000"
echo "📚 API Documentation available at: http://localhost:8000/docs"
echo ""
echo "💡 Tips:"
echo "   - Check logs/backend.log for Python backend logs"
echo "   - Check logs/frontend.log for Next.js frontend logs"
echo "   - Use Ctrl+C to stop all services"
echo ""

# Wait for processes to finish
wait $BACKEND_PID $FRONTEND_PID
