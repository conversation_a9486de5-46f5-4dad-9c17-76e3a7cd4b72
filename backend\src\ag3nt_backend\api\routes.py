"""Main API router for AG3NT Backend."""

from fastapi import APIRouter

from ag3nt_backend.api.v1.projects import router as projects_router
from ag3nt_backend.api.v1.agents import router as agents_router
from ag3nt_backend.api.v1.workflows import router as workflows_router
from ag3nt_backend.api.v1.context import router as context_router


# Create main API router
api_router = APIRouter()

# Include v1 routes
api_router.include_router(
    projects_router,
    prefix="/v1/projects",
    tags=["projects"]
)

api_router.include_router(
    agents_router,
    prefix="/v1/agents",
    tags=["agents"]
)

api_router.include_router(
    workflows_router,
    prefix="/v1/workflows",
    tags=["workflows"]
)

api_router.include_router(
    context_router,
    prefix="/v1/context",
    tags=["context"]
)


@api_router.get("/")
async def api_root():
    """API root endpoint."""
    return {
        "message": "AG3NT Backend API",
        "version": "v1",
        "endpoints": {
            "projects": "/api/v1/projects",
            "agents": "/api/v1/agents",
            "workflows": "/api/v1/workflows",
            "context": "/api/v1/context",
        }
    }
