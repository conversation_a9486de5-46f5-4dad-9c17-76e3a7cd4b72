"""Planning Crew implementation using CrewAI."""

from typing import Any, Dict, List
from crewai import Crew, Task, Process
from crewai.project import CrewB<PERSON>, agent, crew, task

from ag3nt_backend.agents.project_planning_agent import ProjectPlanningAgent
from ag3nt_backend.agents.task_planning_agent import TaskPlanningAgent
from ag3nt_backend.core.logging import LoggerMixin
from ag3nt_backend.core.exceptions import CrewError


class PlanningCrew(LoggerMixin):
    """Crew responsible for project planning and task breakdown."""
    
    def __init__(self):
        self.project_planner = ProjectPlanningAgent()
        self.task_planner = TaskPlanningAgent()
        self.crew_instance = self._create_crew()
        
        self.log_info("Planning crew initialized")
    
    def _create_crew(self) -> Crew:
        """Create the planning crew."""
        try:
            return Crew(
                agents=[
                    self.project_planner.agent,
                    self.task_planner.agent,
                ],
                tasks=[],  # Tasks will be created dynamically
                process=Process.sequential,
                verbose=True,
                memory=True,  # Enable crew memory
            )
        except Exception as e:
            raise CrewError(f"Failed to create planning crew: {str(e)}")
    
    async def analyze_project(self, prompt: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze project requirements."""
        try:
            self.log_info("Starting project analysis", prompt_length=len(prompt))
            
            # Create analysis task
            analysis_task = Task(
                description=f"""
                Analyze the following project requirements and extract key information:
                
                Project Description: {prompt}
                
                Please provide:
                1. Project type and domain
                2. Key features and functionality
                3. Technical complexity assessment
                4. Implementation recommendations
                """,
                expected_output="Structured analysis with project type, features, complexity, and recommendations",
                agent=self.project_planner.agent,
            )
            
            # Execute analysis
            crew = Crew(
                agents=[self.project_planner.agent],
                tasks=[analysis_task],
                process=Process.sequential,
                verbose=True,
            )
            
            result = crew.kickoff()
            
            # Process result through the agent's internal logic
            analysis_result = await self.project_planner.execute_task(
                "analyze_project",
                {"task_type": "analyze", "prompt": prompt, **(context or {})}
            )
            
            self.log_info("Project analysis completed")
            return analysis_result
            
        except Exception as e:
            self.log_error("Project analysis failed", error=str(e))
            raise CrewError(f"Project analysis failed: {str(e)}")
    
    async def clarify_requirements(
        self, 
        analysis: Dict[str, Any], 
        user_answers: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate clarification questions for the project."""
        try:
            self.log_info("Generating clarification questions")
            
            # Create clarification task
            clarification_task = Task(
                description=f"""
                Based on the project analysis, generate clarification questions to better understand the requirements:
                
                Analysis: {analysis}
                Existing Answers: {user_answers or {}}
                
                Generate specific, actionable questions that will help refine the project scope and requirements.
                """,
                expected_output="List of clarification questions with types and priorities",
                agent=self.project_planner.agent,
            )
            
            # Execute clarification
            crew = Crew(
                agents=[self.project_planner.agent],
                tasks=[clarification_task],
                process=Process.sequential,
                verbose=True,
            )
            
            result = crew.kickoff()
            
            # Process result through the agent's internal logic
            clarification_result = await self.project_planner.execute_task(
                "clarify_requirements",
                {
                    "task_type": "clarify",
                    "analysis": analysis,
                    "user_answers": user_answers or {}
                }
            )
            
            self.log_info("Clarification questions generated")
            return clarification_result
            
        except Exception as e:
            self.log_error("Requirements clarification failed", error=str(e))
            raise CrewError(f"Requirements clarification failed: {str(e)}")
    
    async def generate_project_summary(
        self,
        analysis: Dict[str, Any],
        clarifications: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive project summary."""
        try:
            self.log_info("Generating project summary")
            
            # Create summary task
            summary_task = Task(
                description=f"""
                Create a comprehensive project summary based on the analysis and clarifications:
                
                Analysis: {analysis}
                Clarifications: {clarifications or {}}
                
                Include:
                1. Project overview and objectives
                2. Key features and functionality
                3. Technical requirements
                4. Success criteria
                5. Risks and mitigation strategies
                6. Implementation recommendations
                """,
                expected_output="Comprehensive project summary with all key aspects covered",
                agent=self.project_planner.agent,
            )
            
            # Execute summary generation
            crew = Crew(
                agents=[self.project_planner.agent],
                tasks=[summary_task],
                process=Process.sequential,
                verbose=True,
            )
            
            result = crew.kickoff()
            
            # Process result through the agent's internal logic
            summary_result = await self.project_planner.execute_task(
                "generate_summary",
                {
                    "task_type": "summarize",
                    "analysis": analysis,
                    "clarifications": clarifications or {}
                }
            )
            
            self.log_info("Project summary generated")
            return summary_result
            
        except Exception as e:
            self.log_error("Project summary generation failed", error=str(e))
            raise CrewError(f"Project summary generation failed: {str(e)}")
    
    async def breakdown_tasks(
        self,
        project_summary: Dict[str, Any],
        tech_stack: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Break down project into actionable tasks."""
        try:
            self.log_info("Breaking down project into tasks")
            
            # Create task breakdown task
            breakdown_task = Task(
                description=f"""
                Break down the project into detailed, actionable development tasks:
                
                Project Summary: {project_summary}
                Tech Stack: {tech_stack or {}}
                
                Create tasks for:
                1. Project setup and configuration
                2. Feature development
                3. Testing and quality assurance
                4. Deployment and production setup
                
                Include estimates, dependencies, and priorities for each task.
                """,
                expected_output="Detailed task breakdown with estimates, dependencies, and priorities",
                agent=self.task_planner.agent,
            )
            
            # Execute task breakdown
            crew = Crew(
                agents=[self.task_planner.agent],
                tasks=[breakdown_task],
                process=Process.sequential,
                verbose=True,
            )
            
            result = crew.kickoff()
            
            # Process result through the agent's internal logic
            breakdown_result = await self.task_planner.execute_task(
                "breakdown_tasks",
                {
                    "task_type": "breakdown",
                    "project_summary": project_summary,
                    "tech_stack": tech_stack or {}
                }
            )
            
            self.log_info("Task breakdown completed", task_count=len(breakdown_result.get("tasks", [])))
            return breakdown_result
            
        except Exception as e:
            self.log_error("Task breakdown failed", error=str(e))
            raise CrewError(f"Task breakdown failed: {str(e)}")
    
    async def complete_planning_workflow(
        self,
        prompt: str,
        user_answers: Dict[str, Any] = None,
        tech_stack: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute the complete planning workflow."""
        try:
            self.log_info("Starting complete planning workflow")
            
            # Step 1: Analyze project
            analysis = await self.analyze_project(prompt)
            
            # Step 2: Generate clarifications (if needed)
            clarifications = await self.clarify_requirements(analysis, user_answers)
            
            # Step 3: Generate project summary
            summary = await self.generate_project_summary(analysis, clarifications)
            
            # Step 4: Break down into tasks
            tasks = await self.breakdown_tasks(summary, tech_stack)
            
            # Combine all results
            complete_result = {
                "analysis": analysis,
                "clarifications": clarifications,
                "summary": summary,
                "tasks": tasks,
                "workflow_completed": True,
            }
            
            self.log_info("Complete planning workflow finished")
            return complete_result
            
        except Exception as e:
            self.log_error("Complete planning workflow failed", error=str(e))
            raise CrewError(f"Complete planning workflow failed: {str(e)}")
    
    def get_crew_info(self) -> Dict[str, Any]:
        """Get information about the planning crew."""
        return {
            "crew_name": "Planning Crew",
            "agents": [
                self.project_planner.get_agent_info(),
                self.task_planner.get_agent_info(),
            ],
            "capabilities": [
                "Project analysis and requirements extraction",
                "Requirements clarification and question generation",
                "Comprehensive project summary creation",
                "Task breakdown and estimation",
                "Dependency analysis and prioritization",
            ],
        }
