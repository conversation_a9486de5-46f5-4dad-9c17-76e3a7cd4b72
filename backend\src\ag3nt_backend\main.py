"""Main FastAPI application for AG3NT Backend."""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Async<PERSON><PERSON>ator

import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from ag3nt_backend.config import settings
from ag3nt_backend.api.routes import api_router
from ag3nt_backend.core.logging import setup_logging
from ag3nt_backend.core.exceptions import AG3NTException
from ag3nt_backend.core.middleware import (
    LoggingMiddleware,
    MetricsMiddleware,
    RateLimitMiddleware,
)


# Set up structured logging
setup_logging()
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    logger.info("Starting AG3NT Backend", version=settings.app_version)
    
    # Initialize services
    try:
        # Initialize database
        # await init_database()
        
        # Initialize Redis
        # await init_redis()
        
        # Initialize agents and workflows
        # await init_agents()
        
        logger.info("AG3NT Backend started successfully")
        yield
        
    except Exception as e:
        logger.error("Failed to start AG3NT Backend", error=str(e))
        raise
    finally:
        # Cleanup
        logger.info("Shutting down AG3NT Backend")
        # await cleanup_services()


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="Multi-agent system for intelligent project planning and development",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    setup_middleware(app)
    
    # Add routes
    app.include_router(api_router, prefix="/api")
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    # Add health check
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "version": settings.app_version,
            "service": settings.app_name,
        }
    
    return app


def setup_middleware(app: FastAPI) -> None:
    """Set up application middleware."""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=settings.allowed_methods,
        allow_headers=settings.allowed_headers,
    )
    
    # Trusted host middleware
    if not settings.debug:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", "0.0.0.0"]
        )
    
    # Custom middleware
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(MetricsMiddleware)
    app.add_middleware(RateLimitMiddleware)


def setup_exception_handlers(app: FastAPI) -> None:
    """Set up global exception handlers."""
    
    @app.exception_handler(AG3NTException)
    async def ag3nt_exception_handler(request: Request, exc: AG3NTException):
        """Handle AG3NT-specific exceptions."""
        logger.error(
            "AG3NT exception occurred",
            error_type=type(exc).__name__,
            error_message=str(exc),
            path=request.url.path,
            method=request.method,
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_type,
                "message": exc.message,
                "details": exc.details,
            },
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logger.error(
            "Unhandled exception occurred",
            error_type=type(exc).__name__,
            error_message=str(exc),
            path=request.url.path,
            method=request.method,
        )
        return JSONResponse(
            status_code=500,
            content={
                "error": "internal_server_error",
                "message": "An internal server error occurred",
                "details": str(exc) if settings.debug else None,
            },
        )


# Create the application instance
app = create_app()


def main() -> None:
    """Run the application."""
    uvicorn.run(
        "ag3nt_backend.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload,
        workers=settings.api_workers if not settings.api_reload else 1,
        log_level=settings.log_level.lower(),
        access_log=True,
    )


if __name__ == "__main__":
    main()
