"""Custom exceptions for AG3NT Backend."""

from typing import Any, Dict, Optional


class AG3NTException(Exception):
    """Base exception for AG3NT Backend."""
    
    def __init__(
        self,
        message: str,
        error_type: str = "ag3nt_error",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_type = error_type
        self.status_code = status_code
        self.details = details or {}
        super().__init__(message)


class ValidationError(AG3NTException):
    """Validation error."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_type="validation_error",
            status_code=400,
            details=details,
        )


class AuthenticationError(AG3NTException):
    """Authentication error."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_type="authentication_error",
            status_code=401,
        )


class AuthorizationError(AG3NTException):
    """Authorization error."""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            error_type="authorization_error",
            status_code=403,
        )


class NotFoundError(AG3NTException):
    """Resource not found error."""
    
    def __init__(self, message: str, resource_type: str = "resource"):
        super().__init__(
            message=message,
            error_type="not_found_error",
            status_code=404,
            details={"resource_type": resource_type},
        )


class ConflictError(AG3NTException):
    """Resource conflict error."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_type="conflict_error",
            status_code=409,
            details=details,
        )


class RateLimitError(AG3NTException):
    """Rate limit exceeded error."""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(
            message=message,
            error_type="rate_limit_error",
            status_code=429,
        )


class AgentError(AG3NTException):
    """Agent-related error."""
    
    def __init__(
        self,
        message: str,
        agent_id: Optional[str] = None,
        agent_role: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_type="agent_error",
            status_code=500,
            details={
                "agent_id": agent_id,
                "agent_role": agent_role,
            },
        )


class WorkflowError(AG3NTException):
    """Workflow-related error."""
    
    def __init__(
        self,
        message: str,
        workflow_id: Optional[str] = None,
        workflow_node: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_type="workflow_error",
            status_code=500,
            details={
                "workflow_id": workflow_id,
                "workflow_node": workflow_node,
            },
        )


class CrewError(AG3NTException):
    """Crew-related error."""
    
    def __init__(
        self,
        message: str,
        crew_id: Optional[str] = None,
        crew_name: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_type="crew_error",
            status_code=500,
            details={
                "crew_id": crew_id,
                "crew_name": crew_name,
            },
        )


class ContextError(AG3NTException):
    """Context management error."""
    
    def __init__(self, message: str, context_id: Optional[str] = None):
        super().__init__(
            message=message,
            error_type="context_error",
            status_code=500,
            details={"context_id": context_id},
        )


class LLMError(AG3NTException):
    """LLM-related error."""
    
    def __init__(
        self,
        message: str,
        model_name: Optional[str] = None,
        provider: Optional[str] = None,
    ):
        super().__init__(
            message=message,
            error_type="llm_error",
            status_code=500,
            details={
                "model_name": model_name,
                "provider": provider,
            },
        )


class TimeoutError(AG3NTException):
    """Timeout error."""
    
    def __init__(self, message: str, timeout_duration: Optional[float] = None):
        super().__init__(
            message=message,
            error_type="timeout_error",
            status_code=408,
            details={"timeout_duration": timeout_duration},
        )


class ConfigurationError(AG3NTException):
    """Configuration error."""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(
            message=message,
            error_type="configuration_error",
            status_code=500,
            details={"config_key": config_key},
        )
