#!/usr/bin/env python3
"""
AG3NT Backend - Development Server

Run this script to start the AG3NT backend server in development mode.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import and run the main application
from ag3nt_backend.main import main

if __name__ == "__main__":
    print("🚀 Starting AG3NT Backend...")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Health Check: http://localhost:8000/health")
    print("📊 API Root: http://localhost:8000/api")
    print()
    
    main()
