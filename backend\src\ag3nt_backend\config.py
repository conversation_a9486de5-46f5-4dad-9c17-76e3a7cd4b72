"""Configuration management for AG3NT Backend."""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_reload: bool = Field(default=False, env="API_RELOAD")
    api_workers: int = Field(default=1, env="API_WORKERS")
    
    # Database Configuration
    database_url: str = Field(default="sqlite:///./ag3nt.db", env="DATABASE_URL")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # LLM API Keys
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    openrouter_api_key: Optional[str] = Field(default=None, env="OPENROUTER_API_KEY")
    
    # CrewAI Configuration
    crewai_api_key: Optional[str] = Field(default=None, env="CREWAI_API_KEY")
    crewai_telemetry_opt_out: bool = Field(default=True, env="CREWAI_TELEMETRY_OPT_OUT")
    
    # LangChain Configuration
    langchain_tracing_v2: bool = Field(default=False, env="LANGCHAIN_TRACING_V2")
    langchain_api_key: Optional[str] = Field(default=None, env="LANGCHAIN_API_KEY")
    langchain_project: str = Field(default="ag3nt-backend", env="LANGCHAIN_PROJECT")
    
    # Context7 Configuration
    context7_api_key: Optional[str] = Field(default=None, env="CONTEXT7_API_KEY")
    context7_base_url: str = Field(default="https://api.context7.com", env="CONTEXT7_BASE_URL")
    
    # Security Configuration
    secret_key: str = Field(default="change-me-in-production", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS Configuration
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    allowed_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        env="ALLOWED_METHODS"
    )
    allowed_headers: List[str] = Field(default=["*"], env="ALLOWED_HEADERS")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    
    # Application Configuration
    app_name: str = Field(default="AG3NT Backend", env="APP_NAME")
    app_version: str = Field(default="0.1.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    testing: bool = Field(default=False, env="TESTING")
    
    # File Upload Configuration
    max_upload_size: int = Field(default=10485760, env="MAX_UPLOAD_SIZE")  # 10MB
    upload_dir: str = Field(default="./uploads", env="UPLOAD_DIR")
    
    # Rate Limiting
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, env="RATE_LIMIT_WINDOW")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    # WebSocket Configuration
    ws_heartbeat_interval: int = Field(default=30, env="WS_HEARTBEAT_INTERVAL")
    ws_max_connections: int = Field(default=100, env="WS_MAX_CONNECTIONS")
    
    # Agent Configuration
    default_llm_model: str = Field(default="gpt-4o", env="DEFAULT_LLM_MODEL")
    default_llm_temperature: float = Field(default=0.7, env="DEFAULT_LLM_TEMPERATURE")
    max_agent_iterations: int = Field(default=10, env="MAX_AGENT_ITERATIONS")
    agent_timeout: int = Field(default=300, env="AGENT_TIMEOUT")
    
    # Workflow Configuration
    max_workflow_duration: int = Field(default=3600, env="MAX_WORKFLOW_DURATION")
    workflow_checkpoint_interval: int = Field(default=60, env="WORKFLOW_CHECKPOINT_INTERVAL")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def setup_environment(self) -> None:
        """Set up environment variables for external libraries."""
        if self.openai_api_key:
            os.environ["OPENAI_API_KEY"] = self.openai_api_key
        if self.anthropic_api_key:
            os.environ["ANTHROPIC_API_KEY"] = self.anthropic_api_key
        if self.openrouter_api_key:
            os.environ["OPENROUTER_API_KEY"] = self.openrouter_api_key
        if self.crewai_api_key:
            os.environ["CREWAI_API_KEY"] = self.crewai_api_key
        if self.langchain_api_key:
            os.environ["LANGCHAIN_API_KEY"] = self.langchain_api_key
        if self.context7_api_key:
            os.environ["CONTEXT7_API_KEY"] = self.context7_api_key
            
        # Set CrewAI telemetry
        os.environ["CREWAI_TELEMETRY_OPT_OUT"] = str(self.crewai_telemetry_opt_out)
        
        # Set LangChain configuration
        os.environ["LANGCHAIN_TRACING_V2"] = str(self.langchain_tracing_v2)
        os.environ["LANGCHAIN_PROJECT"] = self.langchain_project


# Global settings instance
settings = Settings()

# Set up environment variables
settings.setup_environment()
