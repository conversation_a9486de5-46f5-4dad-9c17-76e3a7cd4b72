# OpenRouter API Key for Claude 3.5 Sonnet
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Your site URL for OpenRouter headers
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Python Backend Configuration
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000

# Additional LLM API Keys (for Python backend)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# CrewAI Configuration
CREWAI_API_KEY=your_crewai_api_key_here
CREWAI_TELEMETRY_OPT_OUT=true

# Context7 Configuration
CONTEXT7_API_KEY=your_context7_api_key_here
CONTEXT7_BASE_URL=https://api.context7.com

# Database Configuration (for production)
DATABASE_URL=postgresql://user:password@localhost/ag3nt
REDIS_URL=redis://localhost:6379/0
