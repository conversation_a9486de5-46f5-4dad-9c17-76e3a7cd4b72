@echo off
setlocal enabledelayedexpansion

echo 🚀 Starting AG3NT Development Environment
echo ==========================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed. Please install Python 3.11 or higher.
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

REM Check if Poetry is available
poetry --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Poetry is not installed. Please install Poetry for Python dependency management.
    echo    Visit: https://python-poetry.org/docs/#installation
    pause
    exit /b 1
)

echo 🔍 Checking environment configuration...

REM Check if .env files exist
if not exist ".env" (
    echo ⚠️  Frontend .env file not found. Copying from .env.example...
    copy .env.example .env >nul
    echo    Please configure your API keys in .env
)

if not exist "backend\.env" (
    echo ⚠️  Backend .env file not found. Copying from backend\.env.example...
    copy backend\.env.example backend\.env >nul
    echo    Please configure your API keys in backend\.env
)

echo ✅ Environment files are ready

echo 📦 Checking dependencies...

REM Check frontend dependencies
if not exist "node_modules" (
    echo    Installing frontend dependencies...
    npm install
)

REM Check backend dependencies
cd backend
if not exist ".venv" (
    if not exist "poetry.lock" (
        echo    Installing backend dependencies...
        poetry install
    )
)
cd ..

echo ✅ Dependencies are ready

REM Create log directory
if not exist "logs" mkdir logs

echo.
echo 🎯 Starting services...
echo    Frontend: http://localhost:3000
echo    Backend API: http://localhost:8000
echo    Backend Docs: http://localhost:8000/docs
echo.
echo 📝 Logs will be saved to:
echo    Frontend: logs\frontend.log
echo    Backend: logs\backend.log
echo.
echo 🛑 Press Ctrl+C to stop all services
echo.

REM Start backend
echo 🐍 Starting Python backend...
cd backend
start /b poetry run python run.py > ..\logs\backend.log 2>&1
cd ..

REM Wait for backend to start
timeout /t 3 /nobreak >nul

REM Test backend health
echo 🔍 Testing backend connection...
curl -s http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Backend is starting but not yet responding. This is normal.
) else (
    echo ✅ Backend is running and healthy
)

REM Start frontend
echo ⚛️  Starting Next.js frontend...
start /b npm run dev > logs\frontend.log 2>&1

REM Wait for frontend to start
timeout /t 3 /nobreak >nul

echo ✅ Frontend is starting...
echo.
echo 🎉 AG3NT Development Environment is ready!
echo.
echo 📱 Open your browser and navigate to: http://localhost:3000
echo 📚 API Documentation available at: http://localhost:8000/docs
echo.
echo 💡 Tips:
echo    - Check logs\backend.log for Python backend logs
echo    - Check logs\frontend.log for Next.js frontend logs
echo    - Use Ctrl+C to stop all services
echo.

REM Keep the window open
echo Press any key to stop all services...
pause >nul

REM Cleanup (this won't work perfectly in batch, but it's better than nothing)
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1

echo ✅ Services stopped
pause
