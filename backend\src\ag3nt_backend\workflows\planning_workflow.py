"""Planning workflow implementation using LangGraph."""

from typing import Any, Dict, List, Literal, Optional
from typing_extensions import TypedDict
from datetime import datetime

from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import Command

from ag3nt_backend.models.state import ProjectState, PlanningState, ProjectStatus
from ag3nt_backend.crews.planning_crew import Planning<PERSON>rew
from ag3nt_backend.core.logging import LoggerMixin
from ag3nt_backend.core.exceptions import WorkflowError


class PlanningWorkflowState(PlanningState):
    """State for the planning workflow."""
    planning_step: str
    needs_user_input: bool
    user_question: Optional[str]
    step_results: Dict[str, Any]


class PlanningWorkflow(LoggerMixin):
    """LangGraph workflow for project planning."""
    
    def __init__(self):
        self.planning_crew = PlanningCrew()
        self.checkpointer = MemorySaver()
        self.workflow = self._build_workflow()
        
        self.log_info("Planning workflow initialized")
    
    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow."""
        try:
            # Create the state graph
            workflow = StateGraph(PlanningWorkflowState)
            
            # Add nodes
            workflow.add_node("initialize", self._initialize_planning)
            workflow.add_node("analyze_project", self._analyze_project)
            workflow.add_node("clarify_requirements", self._clarify_requirements)
            workflow.add_node("wait_for_user_input", self._wait_for_user_input)
            workflow.add_node("generate_summary", self._generate_summary)
            workflow.add_node("select_tech_stack", self._select_tech_stack)
            workflow.add_node("breakdown_tasks", self._breakdown_tasks)
            workflow.add_node("finalize_planning", self._finalize_planning)
            
            # Define the workflow flow
            workflow.add_edge(START, "initialize")
            workflow.add_edge("initialize", "analyze_project")
            
            # Conditional edge from analyze_project
            workflow.add_conditional_edges(
                "analyze_project",
                self._should_clarify_requirements,
                {
                    "clarify": "clarify_requirements",
                    "continue": "generate_summary",
                }
            )
            
            # Conditional edge from clarify_requirements
            workflow.add_conditional_edges(
                "clarify_requirements",
                self._needs_user_input,
                {
                    "wait": "wait_for_user_input",
                    "continue": "generate_summary",
                }
            )
            
            workflow.add_edge("wait_for_user_input", "generate_summary")
            workflow.add_edge("generate_summary", "select_tech_stack")
            workflow.add_edge("select_tech_stack", "breakdown_tasks")
            workflow.add_edge("breakdown_tasks", "finalize_planning")
            workflow.add_edge("finalize_planning", END)
            
            # Compile the workflow
            return workflow.compile(checkpointer=self.checkpointer)
            
        except Exception as e:
            raise WorkflowError(f"Failed to build planning workflow: {str(e)}")
    
    async def execute(
        self,
        project_id: str,
        user_prompt: str,
        user_answers: Dict[str, Any] = None,
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute the planning workflow."""
        try:
            self.log_info("Starting planning workflow execution", project_id=project_id)
            
            # Initial state
            initial_state = PlanningWorkflowState(
                project_id=project_id,
                user_id=config.get("user_id") if config else None,
                session_id=config.get("session_id", project_id) if config else project_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                status=ProjectStatus.PLANNING,
                progress=0.0,
                error_message=None,
                user_prompt=user_prompt,
                user_answers=user_answers or {},
                uploaded_files=[],
                project_type="",
                features=[],
                complexity="",
                domain="",
                requirements={},
                clarifications={},
                summary={},
                tech_stack={},
                prd={},
                wireframes=[],
                filesystem={},
                workflow_definition={},
                tasks=[],
                task_dependencies={},
                context={},
                knowledge_base=[],
                agent_messages=[],
                agent_decisions={},
                design_style_guide=None,
                assets=[],
                planning_phase="initialize",
                planning_iterations=0,
                planning_feedback=[],
                planning_confidence=0.0,
                planning_step="initialize",
                needs_user_input=False,
                user_question=None,
                step_results={},
            )
            
            # Execute workflow
            thread_config = {"configurable": {"thread_id": project_id}}
            result = self.workflow.invoke(initial_state, config=thread_config)
            
            self.log_info("Planning workflow completed", project_id=project_id)
            return result
            
        except Exception as e:
            self.log_error("Planning workflow execution failed", project_id=project_id, error=str(e))
            raise WorkflowError(f"Planning workflow execution failed: {str(e)}", project_id)
    
    def _initialize_planning(self, state: PlanningWorkflowState) -> Command[Literal["analyze_project"]]:
        """Initialize the planning process."""
        self.log_info("Initializing planning", project_id=state["project_id"])
        
        return Command(
            update={
                "planning_step": "analyze_project",
                "progress": 0.1,
                "updated_at": datetime.now(),
            },
            goto="analyze_project"
        )
    
    async def _analyze_project(self, state: PlanningWorkflowState) -> Command[Literal["clarify_requirements", "generate_summary"]]:
        """Analyze the project requirements."""
        self.log_info("Analyzing project", project_id=state["project_id"])
        
        try:
            # Use the planning crew to analyze the project
            analysis_result = await self.planning_crew.analyze_project(
                state["user_prompt"],
                {"project_id": state["project_id"]}
            )
            
            # Update state with analysis results
            updates = {
                "planning_step": "analyze_project_complete",
                "progress": 0.3,
                "updated_at": datetime.now(),
                "step_results": {**state.get("step_results", {}), "analysis": analysis_result},
                "project_type": analysis_result.get("project_type", ""),
                "features": analysis_result.get("features", []),
                "complexity": analysis_result.get("complexity", ""),
                "domain": analysis_result.get("domain", ""),
            }
            
            # Determine next step
            if self._should_clarify_requirements(state):
                return Command(update=updates, goto="clarify_requirements")
            else:
                return Command(update=updates, goto="generate_summary")
                
        except Exception as e:
            self.log_error("Project analysis failed", project_id=state["project_id"], error=str(e))
            return Command(
                update={
                    "error_message": f"Project analysis failed: {str(e)}",
                    "status": ProjectStatus.FAILED,
                    "updated_at": datetime.now(),
                },
                goto="generate_summary"  # Continue with what we have
            )
    
    async def _clarify_requirements(self, state: PlanningWorkflowState) -> Command[Literal["wait_for_user_input", "generate_summary"]]:
        """Generate clarification questions."""
        self.log_info("Clarifying requirements", project_id=state["project_id"])
        
        try:
            analysis = state["step_results"].get("analysis", {})
            
            # Use the planning crew to generate clarifications
            clarification_result = await self.planning_crew.clarify_requirements(
                analysis,
                state["user_answers"]
            )
            
            questions = clarification_result.get("questions", [])
            
            updates = {
                "planning_step": "clarify_requirements_complete",
                "progress": 0.5,
                "updated_at": datetime.now(),
                "step_results": {**state.get("step_results", {}), "clarifications": clarification_result},
            }
            
            # Check if we need user input
            if questions and not state["user_answers"]:
                updates.update({
                    "needs_user_input": True,
                    "user_question": questions[0].get("question") if questions else None,
                })
                return Command(update=updates, goto="wait_for_user_input")
            else:
                return Command(update=updates, goto="generate_summary")
                
        except Exception as e:
            self.log_error("Requirements clarification failed", project_id=state["project_id"], error=str(e))
            return Command(
                update={
                    "error_message": f"Requirements clarification failed: {str(e)}",
                    "updated_at": datetime.now(),
                },
                goto="generate_summary"
            )
    
    def _wait_for_user_input(self, state: PlanningWorkflowState) -> Command[Literal["generate_summary"]]:
        """Wait for user input (this would be handled by the API layer)."""
        self.log_info("Waiting for user input", project_id=state["project_id"])
        
        # In a real implementation, this would pause the workflow
        # and wait for user input via the API
        return Command(
            update={
                "planning_step": "waiting_for_input",
                "updated_at": datetime.now(),
            },
            goto="generate_summary"
        )
    
    async def _generate_summary(self, state: PlanningWorkflowState) -> Command[Literal["select_tech_stack"]]:
        """Generate project summary."""
        self.log_info("Generating project summary", project_id=state["project_id"])
        
        try:
            analysis = state["step_results"].get("analysis", {})
            clarifications = state["step_results"].get("clarifications", {})
            
            # Use the planning crew to generate summary
            summary_result = await self.planning_crew.generate_project_summary(
                analysis,
                clarifications
            )
            
            return Command(
                update={
                    "planning_step": "generate_summary_complete",
                    "progress": 0.7,
                    "updated_at": datetime.now(),
                    "step_results": {**state.get("step_results", {}), "summary": summary_result},
                    "summary": summary_result,
                },
                goto="select_tech_stack"
            )
            
        except Exception as e:
            self.log_error("Summary generation failed", project_id=state["project_id"], error=str(e))
            return Command(
                update={
                    "error_message": f"Summary generation failed: {str(e)}",
                    "updated_at": datetime.now(),
                },
                goto="select_tech_stack"
            )
    
    def _select_tech_stack(self, state: PlanningWorkflowState) -> Command[Literal["breakdown_tasks"]]:
        """Select technology stack (simplified for now)."""
        self.log_info("Selecting tech stack", project_id=state["project_id"])
        
        # Simplified tech stack selection based on project type
        project_type = state.get("project_type", "")
        
        tech_stack = {
            "frontend": "React" if "web" in project_type else "React Native",
            "backend": "Node.js",
            "database": "PostgreSQL",
            "deployment": "Vercel",
        }
        
        return Command(
            update={
                "planning_step": "select_tech_stack_complete",
                "progress": 0.8,
                "updated_at": datetime.now(),
                "tech_stack": tech_stack,
                "step_results": {**state.get("step_results", {}), "tech_stack": tech_stack},
            },
            goto="breakdown_tasks"
        )
    
    async def _breakdown_tasks(self, state: PlanningWorkflowState) -> Command[Literal["finalize_planning"]]:
        """Break down project into tasks."""
        self.log_info("Breaking down tasks", project_id=state["project_id"])
        
        try:
            summary = state.get("summary", {})
            tech_stack = state.get("tech_stack", {})
            
            # Use the planning crew to break down tasks
            tasks_result = await self.planning_crew.breakdown_tasks(summary, tech_stack)
            
            return Command(
                update={
                    "planning_step": "breakdown_tasks_complete",
                    "progress": 0.9,
                    "updated_at": datetime.now(),
                    "tasks": tasks_result.get("tasks", []),
                    "step_results": {**state.get("step_results", {}), "tasks": tasks_result},
                },
                goto="finalize_planning"
            )
            
        except Exception as e:
            self.log_error("Task breakdown failed", project_id=state["project_id"], error=str(e))
            return Command(
                update={
                    "error_message": f"Task breakdown failed: {str(e)}",
                    "updated_at": datetime.now(),
                },
                goto="finalize_planning"
            )
    
    def _finalize_planning(self, state: PlanningWorkflowState) -> Command[Literal[END]]:
        """Finalize the planning process."""
        self.log_info("Finalizing planning", project_id=state["project_id"])
        
        return Command(
            update={
                "planning_step": "completed",
                "status": ProjectStatus.COMPLETED,
                "progress": 1.0,
                "updated_at": datetime.now(),
                "planning_confidence": 0.9,  # High confidence after full workflow
            },
            goto=END
        )
    
    def _should_clarify_requirements(self, state: PlanningWorkflowState) -> Literal["clarify", "continue"]:
        """Determine if requirements clarification is needed."""
        # Simple logic - clarify if no user answers provided
        if not state.get("user_answers"):
            return "clarify"
        return "continue"
    
    def _needs_user_input(self, state: PlanningWorkflowState) -> Literal["wait", "continue"]:
        """Determine if user input is needed."""
        if state.get("needs_user_input", False):
            return "wait"
        return "continue"
