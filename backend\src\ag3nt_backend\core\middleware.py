"""Custom middleware for AG3NT Backend."""

import time
from typing import Callable
from uuid import uuid4

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ag3nt_backend.config import settings


logger = structlog.get_logger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details."""
        # Generate request ID
        request_id = str(uuid4())
        request.state.request_id = request_id
        
        # Log request
        start_time = time.time()
        logger.info(
            "Request started",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Log response
            process_time = time.time() - start_time
            logger.info(
                "Request completed",
                request_id=request_id,
                status_code=response.status_code,
                process_time=process_time,
            )
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # Log error
            process_time = time.time() - start_time
            logger.error(
                "Request failed",
                request_id=request_id,
                error=str(e),
                process_time=process_time,
            )
            raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting metrics."""
    
    def __init__(self, app):
        super().__init__(app)
        self.request_count = 0
        self.request_duration_sum = 0.0
        self.error_count = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and collect metrics."""
        if not settings.enable_metrics:
            return await call_next(request)
        
        start_time = time.time()
        self.request_count += 1
        
        try:
            response = await call_next(request)
            
            # Record metrics
            duration = time.time() - start_time
            self.request_duration_sum += duration
            
            # Add metrics headers
            response.headers["X-Request-Count"] = str(self.request_count)
            response.headers["X-Process-Time"] = f"{duration:.4f}"
            
            return response
            
        except Exception as e:
            self.error_count += 1
            duration = time.time() - start_time
            self.request_duration_sum += duration
            raise


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware."""
    
    def __init__(self, app):
        super().__init__(app)
        self.client_requests = {}  # Simple in-memory store
        self.window_start = time.time()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with rate limiting."""
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # Reset window if needed
        if current_time - self.window_start > settings.rate_limit_window:
            self.client_requests.clear()
            self.window_start = current_time
        
        # Check rate limit
        client_count = self.client_requests.get(client_ip, 0)
        if client_count >= settings.rate_limit_requests:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                request_count=client_count,
                limit=settings.rate_limit_requests,
            )
            return Response(
                content="Rate limit exceeded",
                status_code=429,
                headers={"Retry-After": str(settings.rate_limit_window)},
            )
        
        # Increment counter
        self.client_requests[client_ip] = client_count + 1
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = settings.rate_limit_requests - self.client_requests[client_ip]
        response.headers["X-RateLimit-Limit"] = str(settings.rate_limit_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(self.window_start + settings.rate_limit_window))
        
        return response
