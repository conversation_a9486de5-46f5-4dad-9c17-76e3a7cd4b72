"""Project service for managing projects."""

from typing import Any, Dict, List, Optional
from uuid import uuid4
from datetime import datetime

from ag3nt_backend.models.state import ProjectState, ProjectStatus
from ag3nt_backend.core.logging import LoggerMixin
from ag3nt_backend.core.exceptions import NotFoundError, ValidationError


class ProjectService(LoggerMixin):
    """Service for managing projects."""
    
    def __init__(self):
        # In-memory storage for now (replace with database later)
        self._projects: Dict[str, ProjectState] = {}
        self.log_info("Project service initialized")
    
    async def create_project(
        self,
        user_prompt: str,
        is_interactive: bool = True,
        user_answers: Dict[str, Any] = None,
        design_style_guide: Optional[Dict[str, Any]] = None,
        has_images: bool = False,
        user_id: Optional[str] = None,
    ) -> ProjectState:
        """Create a new project."""
        if not user_prompt.strip():
            raise ValidationError("User prompt cannot be empty")
        
        project_id = str(uuid4())
        session_id = str(uuid4())
        
        project = ProjectState(
            project_id=project_id,
            user_id=user_id,
            session_id=session_id,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            status=ProjectStatus.INITIALIZING,
            progress=0.0,
            error_message=None,
            user_prompt=user_prompt,
            user_answers=user_answers or {},
            uploaded_files=[],
            project_type="",
            features=[],
            complexity="",
            domain="",
            requirements={},
            clarifications={},
            summary={},
            tech_stack={},
            prd={},
            wireframes=[],
            filesystem={},
            workflow_definition={},
            tasks=[],
            task_dependencies={},
            context={
                "is_interactive": is_interactive,
                "has_images": has_images,
                "current_step": "initialize",
                "completed_steps": [],
                "results": {},
            },
            knowledge_base=[],
            agent_messages=[],
            agent_decisions={},
            design_style_guide=design_style_guide,
            assets=[],
        )
        
        # Store project
        self._projects[project_id] = project
        
        self.log_info("Project created", project_id=project_id, prompt_length=len(user_prompt))
        return project
    
    async def get_project(self, project_id: str) -> Optional[ProjectState]:
        """Get a project by ID."""
        project = self._projects.get(project_id)
        if project:
            self.log_info("Project retrieved", project_id=project_id)
        else:
            self.log_warning("Project not found", project_id=project_id)
        return project
    
    async def update_project(
        self,
        project_id: str,
        updates: Dict[str, Any]
    ) -> Optional[ProjectState]:
        """Update a project."""
        project = self._projects.get(project_id)
        if not project:
            return None
        
        # Update fields
        for key, value in updates.items():
            if key in project:
                project[key] = value
        
        # Always update the timestamp
        project["updated_at"] = datetime.now()
        
        self.log_info("Project updated", project_id=project_id, updates=list(updates.keys()))
        return project
    
    async def update_project_status(
        self,
        project_id: str,
        status: ProjectStatus,
        progress: Optional[float] = None,
        error_message: Optional[str] = None
    ) -> Optional[ProjectState]:
        """Update project status and progress."""
        updates = {"status": status}
        
        if progress is not None:
            updates["progress"] = max(0.0, min(1.0, progress))
        
        if error_message is not None:
            updates["error_message"] = error_message
        
        return await self.update_project(project_id, updates)
    
    async def add_project_result(
        self,
        project_id: str,
        step: str,
        result: Dict[str, Any]
    ) -> Optional[ProjectState]:
        """Add a result to the project context."""
        project = self._projects.get(project_id)
        if not project:
            return None
        
        # Update context
        context = project.get("context", {})
        results = context.get("results", {})
        results[step] = result
        context["results"] = results
        
        # Add to completed steps if not already there
        completed_steps = context.get("completed_steps", [])
        if step not in completed_steps:
            completed_steps.append(step)
            context["completed_steps"] = completed_steps
        
        # Update current step
        context["current_step"] = step
        
        # Update project
        project["context"] = context
        project["updated_at"] = datetime.now()
        
        self.log_info("Project result added", project_id=project_id, step=step)
        return project
    
    async def delete_project(self, project_id: str) -> bool:
        """Delete a project."""
        if project_id in self._projects:
            del self._projects[project_id]
            self.log_info("Project deleted", project_id=project_id)
            return True
        else:
            self.log_warning("Project not found for deletion", project_id=project_id)
            return False
    
    async def list_projects(
        self,
        user_id: Optional[str] = None,
        status: Optional[ProjectStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[ProjectState]:
        """List projects with optional filtering."""
        projects = list(self._projects.values())
        
        # Apply filters
        if user_id:
            projects = [p for p in projects if p.get("user_id") == user_id]
        
        if status:
            projects = [p for p in projects if p.get("status") == status]
        
        # Sort by creation date (newest first)
        projects.sort(key=lambda p: p.get("created_at", datetime.min), reverse=True)
        
        # Apply pagination
        projects = projects[offset:offset + limit]
        
        self.log_info("Projects listed", count=len(projects), user_id=user_id, status=status)
        return projects
    
    async def get_project_summary(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of the project."""
        project = self._projects.get(project_id)
        if not project:
            return None
        
        context = project.get("context", {})
        results = context.get("results", {})
        
        return {
            "project_id": project_id,
            "status": project.get("status"),
            "progress": project.get("progress", 0.0),
            "created_at": project.get("created_at"),
            "updated_at": project.get("updated_at"),
            "user_prompt": project.get("user_prompt", "")[:200] + "..." if len(project.get("user_prompt", "")) > 200 else project.get("user_prompt", ""),
            "project_type": project.get("project_type", ""),
            "complexity": project.get("complexity", ""),
            "features_count": len(project.get("features", [])),
            "tasks_count": len(project.get("tasks", [])),
            "completed_steps": context.get("completed_steps", []),
            "current_step": context.get("current_step"),
            "has_results": bool(results),
            "error_message": project.get("error_message"),
        }
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        projects = list(self._projects.values())
        
        status_counts = {}
        for status in ProjectStatus:
            status_counts[status.value] = len([p for p in projects if p.get("status") == status])
        
        return {
            "total_projects": len(projects),
            "status_distribution": status_counts,
            "average_progress": sum(p.get("progress", 0.0) for p in projects) / len(projects) if projects else 0.0,
        }
