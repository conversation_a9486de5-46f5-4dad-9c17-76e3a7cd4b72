"""State models for AG3NT workflows and agents."""

from typing import Any, Dict, List, Optional, Union
from typing_extensions import TypedDict, Annotated
from enum import Enum
from datetime import datetime
import operator


class ProjectStatus(str, Enum):
    """Project status enumeration."""
    INITIALIZING = "initializing"
    PLANNING = "planning"
    DEVELOPING = "developing"
    REVIEWING = "reviewing"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    BLOCKED = "blocked"


class AgentRole(str, Enum):
    """Agent role enumeration."""
    PROJECT_PLANNER = "project_planner"
    TASK_PLANNER = "task_planner"
    CODE_GENERATOR = "code_generator"
    ANALYST = "analyst"
    CONTEXT_MANAGER = "context_manager"


# Base State for all workflows
class BaseState(TypedDict):
    """Base state for all AG3NT workflows."""
    project_id: str
    user_id: Optional[str]
    session_id: str
    created_at: datetime
    updated_at: datetime
    status: ProjectStatus
    progress: float  # 0.0 to 1.0
    error_message: Optional[str]


# Project-specific state
class ProjectState(BaseState):
    """Main project state used across workflows."""
    # User input
    user_prompt: str
    user_answers: Dict[str, Any]
    uploaded_files: List[Dict[str, Any]]
    
    # Project analysis
    project_type: str
    features: List[str]
    complexity: str
    domain: str
    
    # Requirements and clarifications
    requirements: Dict[str, Any]
    clarifications: Dict[str, Any]
    
    # Project artifacts
    summary: Dict[str, Any]
    tech_stack: Dict[str, Any]
    prd: Dict[str, Any]
    wireframes: List[Dict[str, Any]]
    filesystem: Dict[str, Any]
    workflow_definition: Dict[str, Any]
    
    # Tasks and planning
    tasks: Annotated[List[Dict[str, Any]], operator.add]
    task_dependencies: Dict[str, List[str]]
    
    # Context and knowledge
    context: Dict[str, Any]
    knowledge_base: List[Dict[str, Any]]
    
    # Agent interactions
    agent_messages: Annotated[List[Dict[str, Any]], operator.add]
    agent_decisions: Dict[str, Any]
    
    # Design and assets
    design_style_guide: Optional[Dict[str, Any]]
    assets: List[Dict[str, Any]]


# Workflow-specific states
class PlanningState(ProjectState):
    """State for planning workflows."""
    planning_phase: str  # "analysis", "clarification", "summary", etc.
    planning_iterations: int
    planning_feedback: List[Dict[str, Any]]
    planning_confidence: float


class DevelopmentState(ProjectState):
    """State for development workflows."""
    development_phase: str  # "setup", "coding", "testing", "deployment"
    code_artifacts: List[Dict[str, Any]]
    test_results: List[Dict[str, Any]]
    deployment_config: Dict[str, Any]
    code_review_feedback: List[Dict[str, Any]]


class ReviewState(ProjectState):
    """State for review workflows."""
    review_type: str  # "code", "design", "requirements"
    review_criteria: List[str]
    review_feedback: List[Dict[str, Any]]
    review_score: float
    review_approved: bool


# Agent-specific states
class AgentState(TypedDict):
    """State for individual agents."""
    agent_id: str
    agent_role: AgentRole
    agent_name: str
    current_task: Optional[str]
    task_history: List[str]
    performance_metrics: Dict[str, float]
    last_action: Optional[str]
    last_action_timestamp: datetime


# Crew-specific states
class CrewState(TypedDict):
    """State for agent crews."""
    crew_id: str
    crew_name: str
    agents: List[AgentState]
    current_task: Optional[str]
    task_queue: List[str]
    collaboration_history: List[Dict[str, Any]]


# Communication states
class MessageState(TypedDict):
    """State for agent-to-agent communication."""
    message_id: str
    sender_agent: str
    receiver_agent: Optional[str]  # None for broadcast
    message_type: str  # "request", "response", "notification", "error"
    content: Dict[str, Any]
    timestamp: datetime
    priority: int  # 1-10, 10 being highest


# Context management states
class ContextState(TypedDict):
    """State for context management."""
    context_id: str
    context_type: str  # "project", "task", "agent", "global"
    context_data: Dict[str, Any]
    relevance_score: float
    last_accessed: datetime
    access_count: int


# Workflow execution states
class WorkflowState(TypedDict):
    """State for workflow execution tracking."""
    workflow_id: str
    workflow_name: str
    current_node: str
    node_history: List[str]
    execution_start: datetime
    execution_duration: float
    checkpoints: List[Dict[str, Any]]
    
    
# Error handling states
class ErrorState(TypedDict):
    """State for error tracking and recovery."""
    error_id: str
    error_type: str
    error_message: str
    error_context: Dict[str, Any]
    recovery_attempts: int
    max_recovery_attempts: int
    is_recoverable: bool
    recovery_strategy: Optional[str]


# Metrics and monitoring states
class MetricsState(TypedDict):
    """State for metrics and monitoring."""
    metrics_id: str
    workflow_metrics: Dict[str, float]
    agent_metrics: Dict[str, Dict[str, float]]
    performance_metrics: Dict[str, float]
    resource_usage: Dict[str, float]
    timestamp: datetime


# Combined state for complex workflows
class CompleteWorkflowState(ProjectState, AgentState, CrewState, WorkflowState):
    """Complete state combining all aspects of the workflow."""
    pass
