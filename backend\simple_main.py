#!/usr/bin/env python3
"""
Simplified AG3NT Backend - Basic FastAPI implementation

This is a simplified version that works without CrewAI and LangGraph dependencies.
It provides the same API interface but uses basic implementations.
"""

import sys
import os
import json
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import uuid4
from pathlib import Path

# Load environment variables from parent directory
from dotenv import load_dotenv
env_path = Path(__file__).parent.parent / '.env'
load_dotenv(env_path)

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import structlog
import httpx

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# OpenRouter API Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
SITE_URL = os.getenv("NEXT_PUBLIC_SITE_URL", "http://localhost:3000")

if not OPENROUTER_API_KEY:
    raise ValueError("OPENROUTER_API_KEY environment variable is required")

# AI Client for OpenRouter
class AIClient:
    def __init__(self):
        self.api_key = OPENROUTER_API_KEY
        self.base_url = OPENROUTER_BASE_URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": SITE_URL,
            "X-Title": "AG3NT Planning Agent",
            "Content-Type": "application/json"
        }

    async def generate_completion(self, messages: List[Dict[str, str]], model: str = "anthropic/claude-3.5-sonnet") -> str:
        """Generate AI completion using OpenRouter."""
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json={
                        "model": model,
                        "messages": messages,
                        "temperature": 0.7,
                        "max_tokens": 4000,
                        "stream": False
                    }
                )
                response.raise_for_status()
                data = response.json()
                return data["choices"][0]["message"]["content"]
            except Exception as e:
                logger.error("AI generation failed", error=str(e))
                raise HTTPException(status_code=500, detail=f"AI generation failed: {str(e)}")

# Initialize AI client
ai_client = AIClient()

# Create FastAPI app
app = FastAPI(
    title="AG3NT Backend (Simplified)",
    version="0.1.0",
    description="Simplified multi-agent system backend",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://localhost:3002",
        "http://localhost:3003",
        "http://localhost:3004",
        "http://localhost:3005"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# In-memory storage (replace with database in production)
projects_store: Dict[str, Dict[str, Any]] = {}

# Request/Response models
class ProjectCreateRequest(BaseModel):
    prompt: str
    is_interactive: bool = True
    answers: Dict[str, Any] = Field(default_factory=dict)
    design_style_guide: Optional[Dict[str, Any]] = None
    has_images: bool = False

class ProjectResponse(BaseModel):
    project_id: str
    status: str
    progress: float
    user_prompt: str
    created_at: str
    updated_at: str
    results: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None

class ProjectStepRequest(BaseModel):
    step: str
    context: Dict[str, Any] = Field(default_factory=dict)
    answer: Optional[str] = None

class ProjectStepResponse(BaseModel):
    project_id: str
    step: str
    status: str
    result: Dict[str, Any]
    needs_input: bool = False
    question: Optional[str] = None
    next_step: Optional[str] = None

# Simple mock implementations
async def ai_analyze_project(prompt: str) -> Dict[str, Any]:
    """AI-powered deep project analysis using Claude 3.5 Sonnet."""

    analysis_prompt = f"""You are an expert software architect and project analyst. Analyze the following project description and provide a comprehensive analysis in JSON format.

Project Description: {prompt}

Please analyze this project and return a JSON object with the following structure:

{{
    "project_type": "one of: game_application, web_application, mobile_application, desktop_application, backend_service, ai_agent, ecommerce_platform, social_platform",
    "project_confidence": "confidence score 1-20 for project type detection",
    "features": ["list of key features like 3d_graphics, ai_integration, authentication, etc."],
    "feature_details": {{
        "feature_name": {{
            "complexity": "low/medium/high",
            "description": "detailed description",
            "technical_requirements": ["specific requirements"]
        }}
    }},
    "complexity": "low/medium/high/very_high",
    "complexity_score": "numeric score 1-20",
    "complexity_factors": ["list of factors contributing to complexity"],
    "domain": "gaming/ecommerce/social/fintech/healthcare/education/general",
    "technical_hints": ["15 specific technical recommendations"],
    "performance_considerations": ["performance requirements and targets"],
    "architecture_recommendations": ["architectural patterns and approaches"],
    "security_considerations": ["security requirements if applicable"],
    "estimated_timeline_weeks": "realistic timeline in weeks",
    "estimated_timeline": "human readable timeline",
    "risk_factors": ["potential project risks"],
    "team_size_recommendation": "recommended team size",
    "budget_estimate": "estimated budget range",
    "success_metrics": ["measurable success criteria"]
}}

Focus on:
1. Accurate project type detection based on keywords and context
2. Realistic complexity assessment considering all factors
3. Specific, actionable technical recommendations
4. Professional-grade timeline and budget estimates
5. Comprehensive risk assessment

Return only the JSON object, no additional text."""

    messages = [
        {"role": "user", "content": analysis_prompt}
    ]

    try:
        response = await ai_client.generate_completion(messages)
        # Parse the JSON response
        analysis_data = json.loads(response)
        return analysis_data
    except json.JSONDecodeError as e:
        logger.error("Failed to parse AI analysis response", error=str(e), response=response)
        # Fallback to simplified analysis
        return await fallback_analyze_project(prompt)
    except Exception as e:
        logger.error("AI analysis failed", error=str(e))
        # Fallback to simplified analysis
        return await fallback_analyze_project(prompt)


async def fallback_analyze_project(prompt: str) -> Dict[str, Any]:
    """Fallback analysis when AI fails."""
    await asyncio.sleep(1)  # Simulate processing

    # Deep analysis of the prompt
    prompt_lower = prompt.lower()
    words = prompt_lower.split()

    # Advanced project type detection with confidence scoring
    project_types = {
        "game_application": 0,
        "web_application": 0,
        "mobile_application": 0,
        "desktop_application": 0,
        "backend_service": 0,
        "ai_agent": 0,
        "ecommerce_platform": 0,
        "social_platform": 0
    }

    # Game indicators
    game_keywords = ["game", "3d", "three.js", "unity", "unreal", "zombie", "player", "character", "level", "gameplay", "fps", "rpg", "strategy", "action", "adventure", "simulation", "physics", "collision", "animation", "npc", "ai", "multiplayer", "single-player", "power-up", "powerup", "abilities", "infection", "survival", "combat", "weapon", "enemy", "boss", "quest", "mission", "score", "leaderboard", "achievement"]
    for keyword in game_keywords:
        if keyword in prompt_lower:
            project_types["game_application"] += 2 if keyword in ["game", "3d", "three.js", "player", "gameplay"] else 1

    # Web application indicators
    web_keywords = ["website", "web", "app", "dashboard", "portal", "platform", "interface", "frontend", "react", "vue", "angular", "nextjs", "nuxt", "svelte", "html", "css", "javascript", "typescript", "responsive", "mobile-first", "pwa"]
    for keyword in web_keywords:
        if keyword in prompt_lower:
            project_types["web_application"] += 2 if keyword in ["website", "web", "app", "dashboard"] else 1

    # Mobile indicators
    mobile_keywords = ["mobile", "ios", "android", "app", "native", "react-native", "flutter", "ionic", "cordova", "phone", "tablet", "touch", "swipe", "gesture", "notification", "camera", "gps", "location"]
    for keyword in mobile_keywords:
        if keyword in prompt_lower:
            project_types["mobile_application"] += 2 if keyword in ["mobile", "ios", "android", "native"] else 1

    # AI Agent indicators
    ai_keywords = ["agent", "ai", "bot", "chatbot", "assistant", "automation", "machine learning", "ml", "nlp", "llm", "gpt", "claude", "openai", "anthropic", "crew", "langgraph", "langchain", "rag", "vector", "embedding"]
    for keyword in ai_keywords:
        if keyword in prompt_lower:
            project_types["ai_agent"] += 2 if keyword in ["agent", "ai", "bot", "assistant"] else 1

    # E-commerce indicators
    ecommerce_keywords = ["ecommerce", "e-commerce", "shop", "store", "marketplace", "cart", "checkout", "payment", "stripe", "paypal", "product", "inventory", "order", "shipping", "billing", "subscription"]
    for keyword in ecommerce_keywords:
        if keyword in prompt_lower:
            project_types["ecommerce_platform"] += 2 if keyword in ["ecommerce", "e-commerce", "shop", "store"] else 1

    # Determine primary project type
    project_type = max(project_types, key=project_types.get)
    confidence = project_types[project_type]

    # If no clear type, default to web application
    if confidence == 0:
        project_type = "web_application"

    # Comprehensive feature extraction with detailed analysis
    features = []
    feature_details = {}

    # Authentication & User Management
    auth_keywords = ["auth", "login", "signup", "register", "user", "account", "profile", "session", "jwt", "oauth", "sso", "2fa", "security"]
    if any(keyword in prompt_lower for keyword in auth_keywords):
        features.append("authentication")
        feature_details["authentication"] = {
            "complexity": "high" if any(kw in prompt_lower for kw in ["oauth", "sso", "2fa"]) else "medium",
            "methods": ["email/password", "social login"] if "social" in prompt_lower else ["email/password"],
            "security_level": "enterprise" if "enterprise" in prompt_lower else "standard"
        }

    # Data Management & Storage
    data_keywords = ["database", "data", "storage", "crud", "api", "backend", "server", "mongodb", "postgresql", "mysql", "redis", "cache"]
    if any(keyword in prompt_lower for keyword in data_keywords):
        features.append("data_management")
        feature_details["data_management"] = {
            "type": "nosql" if any(kw in prompt_lower for kw in ["mongodb", "nosql"]) else "sql",
            "scale": "enterprise" if "enterprise" in prompt_lower or "scale" in prompt_lower else "standard",
            "real_time": "real-time" in prompt_lower or "live" in prompt_lower
        }

    # Payment & E-commerce
    payment_keywords = ["payment", "billing", "stripe", "paypal", "checkout", "cart", "order", "subscription", "pricing", "monetization"]
    if any(keyword in prompt_lower for keyword in payment_keywords):
        features.append("payment_processing")
        feature_details["payment_processing"] = {
            "providers": ["stripe", "paypal"] if any(kw in prompt_lower for kw in ["stripe", "paypal"]) else ["stripe"],
            "subscription": "subscription" in prompt_lower,
            "marketplace": "marketplace" in prompt_lower
        }

    # 3D Graphics & Rendering
    graphics_keywords = ["3d", "graphics", "rendering", "three.js", "webgl", "shader", "texture", "model", "animation", "lighting", "shadow"]
    if any(keyword in prompt_lower for keyword in graphics_keywords):
        features.append("3d_graphics")
        feature_details["3d_graphics"] = {
            "engine": "three.js" if "three.js" in prompt_lower else "webgl",
            "complexity": "high" if any(kw in prompt_lower for kw in ["shader", "lighting", "shadow"]) else "medium",
            "performance_target": "60fps",
            "features": ["lighting", "shadows", "textures", "animations"]
        }

    # AI & Machine Learning
    ai_keywords = ["ai", "machine learning", "ml", "neural", "algorithm", "intelligent", "smart", "automation", "nlp", "computer vision", "recommendation"]
    if any(keyword in prompt_lower for keyword in ai_keywords):
        features.append("ai_integration")
        feature_details["ai_integration"] = {
            "type": "nlp" if "nlp" in prompt_lower else "general",
            "complexity": "high" if any(kw in prompt_lower for kw in ["neural", "deep learning"]) else "medium",
            "use_cases": ["automation", "recommendations", "analysis"]
        }

    # Real-time Features
    realtime_keywords = ["real-time", "live", "websocket", "socket.io", "streaming", "chat", "notification", "push", "sync"]
    if any(keyword in prompt_lower for keyword in realtime_keywords):
        features.append("real_time_features")
        feature_details["real_time_features"] = {
            "technology": "websockets",
            "use_cases": ["chat", "notifications", "live updates"],
            "scale": "high" if "scale" in prompt_lower else "medium"
        }

    # Multiplayer & Social
    multiplayer_keywords = ["multiplayer", "multi-player", "social", "collaborative", "team", "group", "share", "community"]
    if any(keyword in prompt_lower for keyword in multiplayer_keywords):
        features.append("multiplayer")
        feature_details["multiplayer"] = {
            "type": "real-time" if "real-time" in prompt_lower else "turn-based",
            "max_players": "100+" if "massive" in prompt_lower else "2-10",
            "features": ["matchmaking", "lobbies", "chat"]
        }

    # Physics Engine
    physics_keywords = ["physics", "collision", "gravity", "force", "simulation", "rigid body", "particle", "fluid"]
    if any(keyword in prompt_lower for keyword in physics_keywords):
        features.append("physics_engine")
        feature_details["physics_engine"] = {
            "engine": "cannon.js" if project_type == "game_application" else "matter.js",
            "features": ["collision detection", "gravity", "rigid bodies"],
            "performance": "optimized for 60fps"
        }

    # AI NPCs & Characters
    npc_keywords = ["npc", "character", "ai character", "bot", "enemy", "ally", "behavior", "pathfinding", "state machine"]
    if any(keyword in prompt_lower for keyword in npc_keywords) or ("zombie" in prompt_lower and "game" in prompt_lower):
        features.append("ai_npcs")
        feature_details["ai_npcs"] = {
            "behavior_system": "state machines",
            "pathfinding": "A* algorithm",
            "count": "100+" if "massive" in prompt_lower or "hundreds" in prompt_lower else "10-50",
            "intelligence": "advanced" if "smart" in prompt_lower or "intelligent" in prompt_lower else "standard"
        }

    # Power-up & Progression Systems
    power_keywords = ["power-up", "powerup", "abilities", "skill", "upgrade", "progression", "level", "experience", "xp", "talent", "perk"]
    if any(keyword in prompt_lower for keyword in power_keywords):
        features.append("power_system")
        feature_details["power_system"] = {
            "type": "abilities" if "abilities" in prompt_lower else "items",
            "progression": "linear" if "linear" in prompt_lower else "branching",
            "persistence": "saved" if "save" in prompt_lower else "session"
        }

    # Mobile Features
    mobile_keywords = ["mobile", "touch", "gesture", "swipe", "camera", "gps", "notification", "offline", "pwa"]
    if any(keyword in prompt_lower for keyword in mobile_keywords):
        features.append("mobile_features")
        feature_details["mobile_features"] = {
            "gestures": "touch" in prompt_lower or "swipe" in prompt_lower,
            "camera": "camera" in prompt_lower,
            "location": "gps" in prompt_lower or "location" in prompt_lower,
            "offline": "offline" in prompt_lower
        }

    # Analytics & Monitoring
    analytics_keywords = ["analytics", "tracking", "metrics", "monitoring", "dashboard", "reporting", "insights", "kpi"]
    if any(keyword in prompt_lower for keyword in analytics_keywords):
        features.append("analytics")
        feature_details["analytics"] = {
            "type": "user behavior" if "user" in prompt_lower else "system",
            "real_time": "real-time" in prompt_lower,
            "dashboard": "dashboard" in prompt_lower
        }

    # Advanced complexity determination
    complexity_score = 0
    complexity_factors = []

    # Base complexity from project type
    type_complexity = {
        "game_application": 4,
        "ai_agent": 3,
        "ecommerce_platform": 3,
        "social_platform": 3,
        "mobile_application": 2,
        "web_application": 1,
        "backend_service": 2,
        "desktop_application": 2
    }
    complexity_score += type_complexity.get(project_type, 1)

    # Feature complexity scoring
    feature_complexity = {
        "3d_graphics": 4,
        "physics_engine": 3,
        "ai_npcs": 3,
        "ai_integration": 3,
        "multiplayer": 3,
        "real_time_features": 2,
        "payment_processing": 2,
        "authentication": 1,
        "data_management": 1,
        "power_system": 2,
        "mobile_features": 1,
        "analytics": 1
    }

    for feature in features:
        if feature in feature_complexity:
            complexity_score += feature_complexity[feature]
            if feature_complexity[feature] >= 3:
                complexity_factors.append(feature.replace("_", " ").title())

    # Scale and performance indicators
    scale_keywords = ["massive", "enterprise", "scale", "thousands", "millions", "high-performance", "optimization"]
    if any(keyword in prompt_lower for keyword in scale_keywords):
        complexity_score += 2
        complexity_factors.append("High Scale Requirements")

    # Advanced technical features
    advanced_keywords = ["microservices", "kubernetes", "docker", "ci/cd", "devops", "cloud", "aws", "azure", "gcp", "serverless", "edge", "cdn"]
    if any(keyword in prompt_lower for keyword in advanced_keywords):
        complexity_score += 2
        complexity_factors.append("Advanced Infrastructure")

    # Security requirements
    security_keywords = ["security", "encryption", "compliance", "gdpr", "hipaa", "pci", "audit", "penetration testing"]
    if any(keyword in prompt_lower for keyword in security_keywords):
        complexity_score += 1
        complexity_factors.append("Security Requirements")

    # Determine final complexity
    if complexity_score >= 8:
        complexity = "very_high"
    elif complexity_score >= 5:
        complexity = "high"
    elif complexity_score >= 3:
        complexity = "medium"
    else:
        complexity = "low"

    # Override for explicitly simple projects
    if "simple" in prompt_lower or "basic" in prompt_lower or "minimal" in prompt_lower:
        complexity = "low"
        complexity_factors = ["Explicitly Simple Requirements"]

    # Determine domain
    domain = "general"
    if "game" in prompt_lower:
        domain = "gaming"
    elif "ecommerce" in prompt_lower or "shop" in prompt_lower:
        domain = "ecommerce"
    elif "social" in prompt_lower:
        domain = "social_media"
    elif "finance" in prompt_lower or "banking" in prompt_lower:
        domain = "fintech"

    # Generate comprehensive technical hints based on deep analysis
    technical_hints = []
    performance_considerations = []
    architecture_recommendations = []
    security_considerations = []

    # Project-type specific technical guidance
    if project_type == "game_application":
        technical_hints.extend([
            "Use Three.js r158+ for modern WebGL 2.0 features and better performance",
            "Implement object pooling for frequently created/destroyed entities (bullets, particles)",
            "Use Cannon.js or Ammo.js for physics simulation with worker threads for heavy calculations",
            "Implement frustum culling and LOD (Level of Detail) systems for performance",
            "Use compressed texture formats (DXT, ASTC) and texture atlasing to reduce memory usage",
            "Implement delta time-based movement for frame-rate independent gameplay",
            "Use spatial partitioning (octree/quadtree) for efficient collision detection",
            "Implement audio spatialization with Web Audio API for immersive 3D sound"
        ])
        performance_considerations.extend([
            "Target 60fps on mid-range hardware (GTX 1060 / RX 580 equivalent)",
            "Keep draw calls under 1000 per frame for optimal performance",
            "Limit active physics bodies to 200-300 for stable simulation",
            "Use instanced rendering for repeated objects (trees, buildings, NPCs)",
            "Implement progressive asset loading to reduce initial load time"
        ])
        architecture_recommendations.extend([
            "Use Entity-Component-System (ECS) architecture for scalable game logic",
            "Implement command pattern for undo/redo and replay functionality",
            "Use state machines for game states (menu, playing, paused, game over)",
            "Separate rendering, physics, and game logic into different systems"
        ])

    elif project_type == "ai_agent":
        technical_hints.extend([
            "Use LangChain or LangGraph for complex AI workflow orchestration",
            "Implement RAG (Retrieval Augmented Generation) with vector databases like Pinecone or Weaviate",
            "Use streaming responses for better user experience with long AI generations",
            "Implement conversation memory with sliding window or summarization techniques",
            "Use function calling/tool use for external API integrations",
            "Implement rate limiting and token counting for cost management"
        ])
        architecture_recommendations.extend([
            "Use microservices architecture for different AI capabilities",
            "Implement async processing with message queues (Redis/RabbitMQ)",
            "Use caching layers for frequently requested AI responses",
            "Implement graceful fallbacks for AI service failures"
        ])

    elif project_type == "ecommerce_platform":
        technical_hints.extend([
            "Use Stripe or PayPal for secure payment processing with webhook validation",
            "Implement inventory management with real-time stock tracking",
            "Use CDN for product images and static assets (Cloudflare/AWS CloudFront)",
            "Implement search functionality with Elasticsearch or Algolia",
            "Use Redis for session management and cart persistence",
            "Implement order processing workflows with state machines"
        ])
        security_considerations.extend([
            "PCI DSS compliance for payment data handling",
            "Implement CSRF protection for all state-changing operations",
            "Use HTTPS everywhere with proper certificate management",
            "Implement rate limiting for API endpoints to prevent abuse"
        ])

    # Feature-specific technical guidance
    if "3d_graphics" in features:
        technical_hints.extend([
            "Use glTF 2.0 format for 3D models with Draco compression for smaller file sizes",
            "Implement shadow mapping with cascade shadow maps for large scenes",
            "Use deferred rendering for scenes with many lights",
            "Implement post-processing effects with frame buffers and shaders"
        ])

    if "ai_npcs" in features:
        technical_hints.extend([
            "Use behavior trees for complex AI decision making with visual editors",
            "Implement A* pathfinding with hierarchical pathfinding for large maps",
            "Use influence maps for strategic AI positioning and decision making",
            "Implement flocking algorithms for group NPC behavior",
            "Use finite state machines for individual NPC states (idle, patrol, chase, attack)"
        ])

    if "real_time_features" in features:
        technical_hints.extend([
            "Use WebSockets with Socket.io for reliable real-time communication",
            "Implement client-side prediction and server reconciliation for smooth gameplay",
            "Use message queues (Redis Pub/Sub) for scaling real-time features",
            "Implement heartbeat/ping systems for connection monitoring"
        ])

    if "multiplayer" in features:
        technical_hints.extend([
            "Use authoritative server architecture to prevent cheating",
            "Implement lag compensation techniques (interpolation, extrapolation)",
            "Use UDP for game state updates and TCP for reliable messages",
            "Implement matchmaking systems with skill-based rating algorithms"
        ])

    if "data_management" in features:
        technical_hints.extend([
            "Use database indexing strategies for frequently queried fields",
            "Implement connection pooling for database performance",
            "Use read replicas for scaling read-heavy operations",
            "Implement data validation at both client and server levels"
        ])

    if "authentication" in features:
        security_considerations.extend([
            "Use bcrypt or Argon2 for password hashing with proper salt rounds",
            "Implement JWT tokens with short expiration and refresh token rotation",
            "Use OAuth 2.0 with PKCE for social login integrations",
            "Implement account lockout policies to prevent brute force attacks"
        ])

    # Complexity-based recommendations
    if complexity in ["high", "very_high"]:
        architecture_recommendations.extend([
            "Consider microservices architecture for better scalability and maintainability",
            "Implement comprehensive logging and monitoring with tools like Sentry and DataDog",
            "Use containerization (Docker) and orchestration (Kubernetes) for deployment",
            "Implement CI/CD pipelines with automated testing and deployment",
            "Use feature flags for gradual rollouts and A/B testing"
        ])
        performance_considerations.extend([
            "Implement horizontal scaling with load balancers",
            "Use caching strategies at multiple levels (browser, CDN, application, database)",
            "Implement database sharding for very large datasets",
            "Use async processing for heavy operations to avoid blocking the main thread"
        ])

    # Combine all technical guidance
    all_technical_hints = technical_hints + performance_considerations + architecture_recommendations + security_considerations

    # Ensure we have comprehensive guidance
    if not all_technical_hints:
        all_technical_hints = [
            "Use modern development practices with TypeScript for type safety",
            "Implement comprehensive error handling and logging",
            "Use version control with Git and proper branching strategies",
            "Plan for scalability from the beginning",
            "Focus on user experience and accessibility",
            "Implement proper testing strategies (unit, integration, e2e)"
        ]

    # Calculate detailed timeline estimates
    base_weeks = {
        "low": 2,
        "medium": 6,
        "high": 12,
        "very_high": 20
    }

    timeline_weeks = base_weeks[complexity]

    # Adjust for project type
    if project_type == "game_application":
        timeline_weeks += 4
    elif project_type == "ai_agent":
        timeline_weeks += 2
    elif project_type == "ecommerce_platform":
        timeline_weeks += 3

    # Adjust for feature count
    timeline_weeks += len(features) * 0.5

    # Generate risk assessment
    risk_factors = []
    if complexity in ["high", "very_high"]:
        risk_factors.append("High complexity may lead to scope creep and timeline extensions")
    if "3d_graphics" in features:
        risk_factors.append("3D graphics performance optimization can be challenging across different devices")
    if "ai_npcs" in features:
        risk_factors.append("AI behavior balancing requires extensive playtesting and iteration")
    if "multiplayer" in features:
        risk_factors.append("Network synchronization and latency issues in multiplayer systems")
    if "real_time_features" in features:
        risk_factors.append("Real-time systems require careful architecture to handle scale and failures")

    return {
        "project_type": project_type,
        "project_confidence": confidence,
        "features": features,
        "feature_details": feature_details,
        "complexity": complexity,
        "complexity_score": complexity_score,
        "complexity_factors": complexity_factors,
        "domain": domain,
        "technical_hints": all_technical_hints[:15],  # Limit to most important hints
        "performance_considerations": performance_considerations,
        "architecture_recommendations": architecture_recommendations,
        "security_considerations": security_considerations,
        "estimated_timeline_weeks": int(timeline_weeks),
        "estimated_timeline": f"{int(timeline_weeks)}-{int(timeline_weeks + 4)} weeks",
        "risk_factors": risk_factors,
        "team_size_recommendation": {
            "low": "1-2 developers",
            "medium": "2-3 developers",
            "high": "3-5 developers",
            "very_high": "5-8 developers"
        }[complexity],
        "budget_estimate": {
            "low": "$5k-$15k",
            "medium": "$15k-$50k",
            "high": "$50k-$150k",
            "very_high": "$150k-$500k"
        }[complexity],
        "success_metrics": [
            f"Achieve {60 if '3d_graphics' in features else 95}% performance target on target devices",
            f"Complete development within {int(timeline_weeks + 2)} weeks",
            "Maintain code quality with 90%+ test coverage",
            "User satisfaction rating above 4.0/5.0",
            "Zero critical security vulnerabilities"
        ]
    }

async def mock_generate_tasks(summary: Dict[str, Any]) -> Dict[str, Any]:
    """Mock task generation based on project analysis."""
    await asyncio.sleep(1)

    # Get project details from summary
    project_type = summary.get("project_type", "web_application")
    features = summary.get("features", [])
    complexity = summary.get("complexity", "medium")

    # Base tasks for all projects
    tasks = [
        {
            "id": "task_1",
            "title": "Project Setup & Environment",
            "description": "Initialize project structure, dependencies, and development environment",
            "category": "setup",
            "priority": "high",
            "estimated_hours": 6 if complexity == "high" else 4,
        }
    ]

    # Add project-type specific tasks
    if project_type == "game_application":
        tasks.extend([
            {
                "id": "task_2",
                "title": "3D Scene Setup",
                "description": "Set up Three.js scene, camera, lighting, and basic 3D environment",
                "category": "graphics",
                "priority": "high",
                "estimated_hours": 12,
            },
            {
                "id": "task_3",
                "title": "Character Controller",
                "description": "Implement zombie character movement and controls",
                "category": "gameplay",
                "priority": "high",
                "estimated_hours": 16,
            },
            {
                "id": "task_4",
                "title": "AI System",
                "description": "Develop NPC AI behaviors and pathfinding",
                "category": "ai",
                "priority": "high",
                "estimated_hours": 20,
            },
            {
                "id": "task_5",
                "title": "Infection Mechanics",
                "description": "Implement infection system and chain reactions",
                "category": "gameplay",
                "priority": "high",
                "estimated_hours": 18,
            }
        ])
    else:
        # Standard web application tasks
        tasks.extend([
            {
                "id": "task_2",
                "title": "Frontend Development",
                "description": "Build user interface and components",
                "category": "frontend",
                "priority": "high",
                "estimated_hours": 20,
            },
            {
                "id": "task_3",
                "title": "Backend Development",
                "description": "Implement server-side logic and APIs",
                "category": "backend",
                "priority": "high",
                "estimated_hours": 16,
            }
        ])

    # Add feature-specific tasks
    if "3d_graphics" in features:
        tasks.append({
            "id": f"task_{len(tasks) + 1}",
            "title": "Graphics Optimization",
            "description": "Optimize 3D rendering performance and visual quality",
            "category": "optimization",
            "priority": "medium",
            "estimated_hours": 12,
        })

    if "physics_engine" in features:
        tasks.append({
            "id": f"task_{len(tasks) + 1}",
            "title": "Physics Implementation",
            "description": "Add collision detection and physics simulation",
            "category": "physics",
            "priority": "medium",
            "estimated_hours": 14,
        })

    if "power_system" in features:
        tasks.append({
            "id": f"task_{len(tasks) + 1}",
            "title": "Power-up System",
            "description": "Implement power-ups and special abilities",
            "category": "gameplay",
            "priority": "medium",
            "estimated_hours": 10,
        })

    # Always add testing and deployment
    tasks.extend([
        {
            "id": f"task_{len(tasks) + 1}",
            "title": "Testing & Quality Assurance",
            "description": "Comprehensive testing and bug fixes",
            "category": "testing",
            "priority": "medium",
            "estimated_hours": 12 if complexity == "high" else 8,
        },
        {
            "id": f"task_{len(tasks) + 1}",
            "title": "Deployment & Launch",
            "description": "Deploy to production and launch preparation",
            "category": "deployment",
            "priority": "medium",
            "estimated_hours": 6,
        }
    ])

    total_hours = sum(task["estimated_hours"] for task in tasks)
    categories = list(set(task["category"] for task in tasks))

    return {
        "tasks": tasks,
        "total_estimated_hours": total_hours,
        "task_categories": categories,
        "estimated_timeline": f"{total_hours // 40 + 1}-{total_hours // 30 + 1} weeks",
        "team_size_recommendation": "3-5 developers" if complexity == "high" else "2-3 developers"
    }


async def ai_generate_clarification_questions(prompt: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
    """AI-powered clarification questions generation."""

    clarify_prompt = f"""You are an expert project manager. Based on the project description and analysis, generate 5-7 targeted clarification questions to gather additional details needed for comprehensive planning.

Project Description: {prompt}

Project Analysis:
- Type: {analysis.get('project_type', 'unknown')}
- Complexity: {analysis.get('complexity', 'unknown')}
- Features: {', '.join(analysis.get('features', []))}
- Domain: {analysis.get('domain', 'unknown')}

Generate clarification questions in JSON format:

{{
    "questions": [
        {{
            "id": "unique_id",
            "question": "Clear, specific question",
            "type": "multiple_choice/boolean/text/range",
            "options": ["option1", "option2"] (if multiple_choice),
            "default": "default_value",
            "rationale": "Why this question is important"
        }}
    ],
    "total_questions": "number of questions",
    "estimated_time": "time to complete",
    "priority": "high/medium/low"
}}

Focus on:
1. Technical requirements and constraints
2. Target audience and platform preferences
3. Performance and scalability requirements
4. Budget and timeline constraints
5. Integration requirements
6. Security and compliance needs
7. User experience preferences

Make questions specific to the project type and complexity. Return only JSON."""

    messages = [
        {"role": "user", "content": clarify_prompt}
    ]

    try:
        response = await ai_client.generate_completion(messages)
        return json.loads(response)
    except Exception as e:
        logger.error("AI clarification generation failed", error=str(e))
        return await fallback_generate_clarification_questions(prompt, analysis)


async def fallback_generate_clarification_questions(prompt: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Fallback clarification questions when AI fails."""
    await asyncio.sleep(0.5)

    project_type = analysis.get("project_type", "web_application")
    complexity = analysis.get("complexity", "medium")
    features = analysis.get("features", [])

    questions = []

    if project_type == "game_application":
        questions.extend([
            {
                "id": "target_platform",
                "question": "What platforms should the game target?",
                "type": "multiple_choice",
                "options": ["Web Browser", "Desktop (Electron)", "Mobile (PWA)", "All platforms"],
                "default": "Web Browser"
            },
            {
                "id": "graphics_quality",
                "question": "What level of graphics quality are you targeting?",
                "type": "multiple_choice",
                "options": ["High-end (RTX capable)", "Mid-range (GTX 1060+)", "Low-end (Integrated graphics)", "Adaptive quality"],
                "default": "Mid-range (GTX 1060+)"
            },
            {
                "id": "multiplayer_scope",
                "question": "Do you plan to add multiplayer features in the future?",
                "type": "boolean",
                "default": False
            }
        ])

    if "ai_npcs" in features:
        questions.append({
            "id": "npc_count",
            "question": "How many NPCs should be active simultaneously?",
            "type": "multiple_choice",
            "options": ["10-20 (Small scale)", "50-100 (Medium scale)", "200+ (Large scale)"],
            "default": "50-100 (Medium scale)"
        })

    if "3d_graphics" in features:
        questions.append({
            "id": "art_style",
            "question": "What art style do you prefer?",
            "type": "multiple_choice",
            "options": ["Realistic", "Stylized/Cartoon", "Low-poly", "Pixel art 3D"],
            "default": "Stylized/Cartoon"
        })

    # Add general questions for complex projects
    if complexity == "high":
        questions.extend([
            {
                "id": "timeline_priority",
                "question": "What's more important for this project?",
                "type": "multiple_choice",
                "options": ["Fast delivery", "High quality", "Feature completeness", "Balanced approach"],
                "default": "Balanced approach"
            },
            {
                "id": "budget_range",
                "question": "What's your development budget range?",
                "type": "multiple_choice",
                "options": ["Under $10k", "$10k-$50k", "$50k-$100k", "$100k+"],
                "default": "$10k-$50k"
            }
        ])

    return {
        "questions": questions,
        "total_questions": len(questions),
        "estimated_time": "5-10 minutes to complete"
    }


async def generate_project_summary(prompt: str, analysis: Dict[str, Any], clarify: Dict[str, Any]) -> Dict[str, Any]:
    """Generate comprehensive project summary."""
    await asyncio.sleep(1)

    project_type = analysis.get("project_type", "web_application")
    features = analysis.get("features", [])
    complexity = analysis.get("complexity", "medium")
    domain = analysis.get("domain", "general")

    # Generate detailed project overview
    if project_type == "game_application":
        project_overview = f"""
A sophisticated 3D reverse-zombie game that revolutionizes the traditional zombie genre by putting players in control of the infection.
Built with Three.js and modern web technologies, this {complexity}-complexity game features:

• **Unique Gameplay Mechanic**: Players control a zombie character and strategically spread infection
• **3D Environment**: Fully immersive 3D world with optimized graphics and physics
• **AI-Driven NPCs**: Intelligent human characters with realistic survival behaviors
• **Infection Chain Reactions**: Dynamic spreading mechanics that create emergent gameplay
• **Multiple Zombie Types**: Various mutations with unique abilities and characteristics
• **Power-up System**: Upgradeable zombie capabilities and special powers
• **Progressive Difficulty**: Dynamic scaling based on infection count and player performance
• **Multiple Maps**: Diverse environments with varying layouts and strategic challenges

The game combines strategy and action elements, requiring players to think tactically about infection spread while managing resources and adapting to increasingly challenging scenarios.
        """.strip()
    else:
        project_overview = f"A comprehensive {project_type} in the {domain} domain with {complexity} complexity."

    # Generate key features with descriptions
    feature_descriptions = {
        "3d_graphics": "Advanced 3D rendering with Three.js, including lighting, shadows, and particle effects",
        "ai_integration": "Machine learning algorithms for dynamic difficulty adjustment and behavior prediction",
        "physics_engine": "Realistic collision detection, gravity simulation, and object interactions",
        "ai_npcs": "Intelligent non-player characters with state machines, pathfinding, and behavioral trees",
        "power_system": "Upgradeable abilities, special powers, and progression mechanics",
        "real_time_features": "Live updates, real-time communication, and synchronized gameplay",
        "multiplayer": "Multi-player support with networking, matchmaking, and session management"
    }

    detailed_features = []
    for feature in features:
        if feature in feature_descriptions:
            detailed_features.append({
                "name": feature.replace("_", " ").title(),
                "description": feature_descriptions[feature],
                "priority": "high" if feature in ["3d_graphics", "ai_npcs"] else "medium"
            })

    # Technical requirements based on project type
    if project_type == "game_application":
        technical_requirements = [
            "WebGL 2.0 support for advanced graphics",
            "Modern browser with ES6+ support",
            "Minimum 4GB RAM for smooth performance",
            "Dedicated graphics card recommended",
            "60fps target performance",
            "Responsive design for various screen sizes",
            "Progressive loading for large assets",
            "Efficient memory management for NPCs",
            "Optimized collision detection algorithms",
            "Audio system with 3D spatial sound"
        ]
    else:
        technical_requirements = [
            "Modern web technologies (HTML5, CSS3, ES6+)",
            "Responsive design for mobile and desktop",
            "Cross-browser compatibility",
            "Performance optimization",
            "Accessibility compliance (WCAG 2.1)",
            "SEO optimization"
        ]

    return {
        "project_overview": project_overview,
        "key_features": detailed_features,
        "technical_requirements": technical_requirements,
        "target_audience": "Gamers aged 16-35 who enjoy strategy and action games",
        "unique_selling_points": [
            "First reverse-zombie game mechanic",
            "Strategic infection gameplay",
            "Advanced AI behaviors",
            "Immersive 3D environment"
        ],
        "success_metrics": [
            "Player retention rate > 70%",
            "Average session time > 15 minutes",
            "60fps performance on mid-range hardware",
            "Loading time < 10 seconds"
        ]
    }


async def generate_tech_stack(prompt: str, analysis: Dict[str, Any], summary: Dict[str, Any]) -> Dict[str, Any]:
    """Generate detailed technology stack recommendations."""
    await asyncio.sleep(1)

    project_type = analysis.get("project_type", "web_application")
    features = analysis.get("features", [])
    complexity = analysis.get("complexity", "medium")

    if project_type == "game_application":
        tech_stack = {
            "frontend": {
                "primary": "Three.js",
                "version": "r158+",
                "description": "3D graphics library for WebGL rendering",
                "alternatives": ["Babylon.js", "PlayCanvas"],
                "supporting_libraries": [
                    "React (UI components)",
                    "TypeScript (type safety)",
                    "Vite (build tool)",
                    "Cannon.js (physics)",
                    "Howler.js (audio)"
                ]
            },
            "backend": {
                "primary": "Node.js",
                "framework": "Express.js",
                "description": "Lightweight backend for game state and leaderboards",
                "alternatives": ["Python Flask", "Go Gin"],
                "supporting_libraries": [
                    "Socket.io (real-time communication)",
                    "JWT (authentication)",
                    "Helmet (security)",
                    "CORS (cross-origin requests)"
                ]
            },
            "database": {
                "primary": "MongoDB",
                "description": "Document database for game data and player progress",
                "alternatives": ["PostgreSQL", "Redis"],
                "use_cases": [
                    "Player profiles and statistics",
                    "Game state persistence",
                    "Leaderboards and achievements",
                    "Game configuration data"
                ]
            },
            "deployment": {
                "primary": "Vercel",
                "description": "Edge deployment for optimal performance",
                "alternatives": ["Netlify", "AWS CloudFront"],
                "cdn": "Cloudflare (asset delivery)",
                "monitoring": "Sentry (error tracking)"
            },
            "development_tools": {
                "version_control": "Git + GitHub",
                "package_manager": "npm/yarn",
                "testing": "Jest + Cypress",
                "linting": "ESLint + Prettier",
                "bundler": "Vite",
                "3d_tools": "Blender (asset creation)"
            }
        }
    else:
        # Standard web application stack
        tech_stack = {
            "frontend": {
                "primary": "React",
                "version": "18+",
                "description": "Modern UI library with hooks and context",
                "supporting_libraries": ["TypeScript", "Tailwind CSS", "React Router"]
            },
            "backend": {
                "primary": "Node.js",
                "framework": "Express.js",
                "description": "RESTful API server",
                "supporting_libraries": ["JWT", "bcrypt", "Helmet", "CORS"]
            },
            "database": {
                "primary": "PostgreSQL",
                "description": "Relational database for structured data",
                "alternatives": ["MongoDB", "MySQL"]
            },
            "deployment": {
                "primary": "Vercel",
                "description": "Serverless deployment platform",
                "alternatives": ["Netlify", "Railway"]
            }
        }

    return {
        **tech_stack,
        "architecture_pattern": "Client-Server with CDN",
        "performance_targets": {
            "initial_load": "< 3 seconds",
            "fps": "60fps (for games)",
            "bundle_size": "< 2MB initial",
            "lighthouse_score": "> 90"
        },
        "scalability_considerations": [
            "Asset optimization and compression",
            "Code splitting and lazy loading",
            "CDN for static assets",
            "Database indexing and optimization"
        ]
    }


async def generate_prd(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate Product Requirements Document."""
    await asyncio.sleep(1)

    analysis = context.get("analyze", {})
    summary = context.get("summary", {})

    return {
        "document_type": "Product Requirements Document (PRD)",
        "version": "1.0",
        "last_updated": "2025-07-12",
        "overview": {
            "product_name": "3D Reverse Zombie Game",
            "product_vision": "Revolutionary zombie game where players control the infection",
            "target_market": "PC and web gamers aged 16-35",
            "business_objectives": [
                "Create unique gaming experience",
                "Build engaged player community",
                "Establish new game genre",
                "Generate revenue through premium features"
            ]
        },
        "functional_requirements": [
            {
                "id": "FR-001",
                "title": "3D Game Environment",
                "description": "Fully rendered 3D world with buildings, terrain, and interactive objects",
                "priority": "High",
                "acceptance_criteria": [
                    "60fps performance on mid-range hardware",
                    "Multiple detailed maps with unique layouts",
                    "Dynamic lighting and shadows",
                    "Collision detection for all objects"
                ]
            },
            {
                "id": "FR-002",
                "title": "Zombie Character Control",
                "description": "Intuitive controls for zombie character movement and actions",
                "priority": "High",
                "acceptance_criteria": [
                    "WASD movement with mouse look",
                    "Infection ability with visual feedback",
                    "Special abilities for different zombie types",
                    "Responsive controls with < 50ms input lag"
                ]
            },
            {
                "id": "FR-003",
                "title": "AI Human NPCs",
                "description": "Intelligent human characters with survival behaviors",
                "priority": "High",
                "acceptance_criteria": [
                    "Pathfinding to avoid zombies",
                    "Group behavior and communication",
                    "Realistic fear and panic responses",
                    "Performance optimization for 100+ NPCs"
                ]
            }
        ],
        "non_functional_requirements": [
            {
                "category": "Performance",
                "requirements": [
                    "60fps on GTX 1060 or equivalent",
                    "Loading time < 10 seconds",
                    "Memory usage < 2GB",
                    "Smooth gameplay with 200+ entities"
                ]
            },
            {
                "category": "Usability",
                "requirements": [
                    "Intuitive controls learnable in < 5 minutes",
                    "Clear visual feedback for all actions",
                    "Accessibility support for colorblind players",
                    "Responsive UI for different screen sizes"
                ]
            }
        ],
        "user_stories": [
            {
                "id": "US-001",
                "as_a": "Player",
                "i_want": "to control a zombie character",
                "so_that": "I can experience the game from the zombie perspective",
                "acceptance_criteria": [
                    "Character responds to input within 50ms",
                    "Movement feels natural and fluid",
                    "Visual feedback confirms actions"
                ]
            },
            {
                "id": "US-002",
                "as_a": "Player",
                "i_want": "to infect human NPCs",
                "so_that": "I can grow my zombie army",
                "acceptance_criteria": [
                    "Infection spreads on contact",
                    "Visual transformation animation",
                    "Infected humans become allies"
                ]
            }
        ],
        "success_metrics": [
            "Player retention > 70% after first week",
            "Average session time > 15 minutes",
            "User rating > 4.0/5.0",
            "Performance targets met on target hardware"
        ]
    }


async def generate_database_design(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate database schema and design."""
    await asyncio.sleep(1)

    return {
        "database_type": "MongoDB (Document Database)",
        "rationale": "Flexible schema for game data, good performance for read-heavy operations",
        "collections": [
            {
                "name": "players",
                "description": "Player profiles and statistics",
                "schema": {
                    "_id": "ObjectId",
                    "username": "String (unique)",
                    "email": "String (unique)",
                    "created_at": "Date",
                    "last_login": "Date",
                    "statistics": {
                        "games_played": "Number",
                        "total_infections": "Number",
                        "best_score": "Number",
                        "total_playtime": "Number"
                    },
                    "achievements": ["Array of ObjectId"],
                    "settings": {
                        "graphics_quality": "String",
                        "audio_volume": "Number",
                        "controls": "Object"
                    }
                },
                "indexes": ["username", "email", "statistics.best_score"]
            },
            {
                "name": "game_sessions",
                "description": "Individual game session data",
                "schema": {
                    "_id": "ObjectId",
                    "player_id": "ObjectId",
                    "map_id": "String",
                    "start_time": "Date",
                    "end_time": "Date",
                    "final_score": "Number",
                    "infections_count": "Number",
                    "survival_time": "Number",
                    "zombie_types_used": ["Array of String"],
                    "powerups_collected": ["Array of String"],
                    "game_events": ["Array of Object"]
                },
                "indexes": ["player_id", "start_time", "final_score"]
            },
            {
                "name": "leaderboards",
                "description": "Global and map-specific leaderboards",
                "schema": {
                    "_id": "ObjectId",
                    "map_id": "String",
                    "player_id": "ObjectId",
                    "username": "String",
                    "score": "Number",
                    "infections": "Number",
                    "date_achieved": "Date",
                    "rank": "Number"
                },
                "indexes": ["map_id", "score", "date_achieved"]
            },
            {
                "name": "game_maps",
                "description": "Map configurations and metadata",
                "schema": {
                    "_id": "ObjectId",
                    "name": "String",
                    "description": "String",
                    "difficulty": "String",
                    "layout_data": "Object",
                    "spawn_points": ["Array of Object"],
                    "npc_count": "Number",
                    "environment_settings": "Object",
                    "unlock_requirements": "Object"
                },
                "indexes": ["name", "difficulty"]
            }
        ],
        "data_relationships": [
            "players -> game_sessions (one-to-many)",
            "players -> leaderboards (one-to-many)",
            "game_maps -> game_sessions (one-to-many)",
            "game_maps -> leaderboards (one-to-many)"
        ],
        "performance_considerations": [
            "Index on frequently queried fields",
            "Aggregate pipelines for leaderboard calculations",
            "TTL indexes for temporary session data",
            "Sharding strategy for high-volume data"
        ],
        "backup_strategy": {
            "frequency": "Daily automated backups",
            "retention": "30 days for daily, 12 months for weekly",
            "location": "Cloud storage with geographic redundancy"
        }
    }


async def ai_generate_wireframes(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """AI-powered wireframe generation with ASCII mockups."""

    analysis = context.get("analyze", {})
    summary = context.get("summary", {})

    wireframe_prompt = f"""You are an expert UX/UI designer. Create detailed wireframes with ASCII art mockups for this project.

Project Description: {prompt}

Project Analysis:
- Type: {analysis.get('project_type', 'unknown')}
- Features: {', '.join(analysis.get('features', []))}
- Complexity: {analysis.get('complexity', 'unknown')}

Project Summary:
- Overview: {summary.get('project_overview', 'Not available')[:200]}...

Create wireframes in JSON format with ASCII art mockups:

{{
    "wireframe_type": "descriptive title",
    "pages": [
        {{
            "name": "Page Name",
            "type": "landing/dashboard/game/etc",
            "purpose": "Clear purpose description",
            "wireframe": "ASCII art representation using box drawing characters",
            "components": ["list of UI components"],
            "interactions": ["list of user interactions"],
            "responsive_notes": "mobile/tablet considerations"
        }}
    ],
    "design_principles": ["key design principles"],
    "responsive_breakpoints": ["breakpoint specifications"],
    "accessibility_notes": ["accessibility considerations"]
}}

Requirements for ASCII wireframes:
1. Use box drawing characters: ╔═╗║╚╝┌─┐│└┘
2. Show clear layout structure and hierarchy
3. Include specific UI elements relevant to the project type
4. Make wireframes 80 characters wide maximum
5. Show 4-6 key pages/screens
6. Include navigation, content areas, and interactive elements
7. Use emojis sparingly for icons (🎮🏠⚙️📊🔍)

For game projects: Include game HUD, main menu, game over screen
For web apps: Include landing page, dashboard, settings
For mobile apps: Include home screen, navigation, key features

Return only JSON with detailed ASCII wireframes."""

    messages = [
        {"role": "user", "content": wireframe_prompt}
    ]

    try:
        response = await ai_client.generate_completion(messages)
        return json.loads(response)
    except Exception as e:
        logger.error("AI wireframe generation failed", error=str(e))
        return await fallback_generate_wireframes(prompt, context)


async def fallback_generate_wireframes(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Fallback wireframes when AI fails."""
    await asyncio.sleep(1)

    analysis = context.get("analyze", {})
    project_type = analysis.get("project_type", "web_application")

    if project_type == "game_application":
        # Game-specific wireframes with ASCII art
        return {
            "wireframe_type": "Game UI Wireframes with ASCII Mockups",
            "pages": [
                {
                    "name": "Main Menu",
                    "type": "landing",
                    "purpose": "Entry point with game options and branding",
                    "wireframe": """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                          🧟 ZOMBIE INFECTION GAME 🧟                        ║
║                                                                              ║
║                              ┌─────────────────┐                            ║
║                              │   🎮 PLAY GAME  │                            ║
║                              └─────────────────┘                            ║
║                                                                              ║
║                              ┌─────────────────┐                            ║
║                              │  🗺️  SELECT MAP  │                            ║
║                              └─────────────────┘                            ║
║                                                                              ║
║                              ┌─────────────────┐                            ║
║                              │  🏆 LEADERBOARD │                            ║
║                              └─────────────────┘                            ║
║                                                                              ║
║                              ┌─────────────────┐                            ║
║                              │  ⚙️  SETTINGS   │                            ║
║                              └─────────────────┘                            ║
║                                                                              ║
║  [Background: Animated 3D city scene with zombies and humans]               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝""",
                    "components": [
                        "Game logo/title (centered, large)",
                        "Play button (primary CTA)",
                        "Map selection button",
                        "Leaderboard button",
                        "Settings button",
                        "Animated 3D background scene"
                    ],
                    "interactions": [
                        "Hover effects with zombie growl sounds",
                        "Smooth transitions between screens",
                        "Background animation with moving NPCs"
                    ]
                },
                {
                    "name": "Game HUD",
                    "type": "overlay",
                    "purpose": "In-game user interface during gameplay",
                    "wireframe": """
╔══════════════════════════════════════════════════════════════════════════════╗
║ ❤️ Health: ████████░░ 80%    🧟 Infections: 47    💯 Score: 12,450    ⏸️ ║
║                                                                              ║
║ 🔋 Energy: ██████████ 100%                                                  ║
║                                                                              ║
║                                                                              ║
║                          [3D GAME WORLD VIEW]                               ║
║                                                                              ║
║                        Player controls zombie here                          ║
║                                                                              ║
║                     Humans run around trying to escape                      ║
║                                                                              ║
║                                                                              ║
║ 💊 Power-ups:                                                               ║
║ ┌─────────┐                                                                 ║
║ │ 🏃 Speed │                                                                 ║
║ │ Ready   │                                                                 ║
║ └─────────┘                                                                 ║
║ ┌─────────┐                                                                 ║
║ │ 🦠 Virus │                                                                 ║
║ │ 3.2s    │                                                                 ║
║ └─────────┘                                                    ┌──────────┐ ║
║                                                                │ MINI-MAP │ ║
║                                                                │ ●●●○○○○○ │ ║
║                                                                │ ●🧟●○○○○ │ ║
║                                                                │ ●●●○○○○○ │ ║
║                                                                └──────────┘ ║
╚══════════════════════════════════════════════════════════════════════════════╝""",
                    "components": [
                        "Health bar (top-left)",
                        "Infection counter (top-center)",
                        "Score display (top-right)",
                        "Energy bar (left side)",
                        "Power-up status (bottom-left)",
                        "Mini-map (bottom-right)",
                        "Pause button (top-right corner)"
                    ],
                    "interactions": [
                        "Real-time health/energy updates",
                        "Pulsing effects for low health",
                        "Power-up cooldown animations",
                        "Mini-map shows infected areas"
                    ]
                },
                {
                    "name": "Map Selection",
                    "type": "selection",
                    "purpose": "Choose game map and difficulty settings",
                    "wireframe": """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              🗺️ SELECT MAP 🗺️                               ║
║                                                                              ║
║  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             ║
║  │   CITY CENTER   │  │  SHOPPING MALL  │  │    HOSPITAL     │             ║
║  │                 │  │                 │  │                 │             ║
║  │  🏢🏢🏢🏢🏢🏢🏢  │  │  🛒🛍️🏬🛍️🛒🏬🛒  │  │  🏥⚕️🚑⚕️🏥⚕️🏥  │             ║
║  │  🏢🧟🏢👥🏢🏢🏢  │  │  🛒👥🏬🧟🛍️🏬🛒  │  │  🏥👥⚕️🧟🚑⚕️🏥  │             ║
║  │  🏢🏢🏢🏢🏢🏢🏢  │  │  🛒🛍️🏬🛍️🛒🏬🛒  │  │  🏥⚕️🚑⚕️🏥⚕️🏥  │             ║
║  │                 │  │                 │  │                 │             ║
║  │ Difficulty: ⭐⭐⭐ │  │ Difficulty: ⭐⭐  │  │ Difficulty: ⭐⭐⭐⭐ │             ║
║  │ Best: 8,420     │  │ Best: 12,100    │  │ Best: 5,680     │             ║
║  └─────────────────┘  └─────────────────┘  └─────────────────┘             ║
║                                                                              ║
║  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             ║
║  │     SCHOOL      │  │   OFFICE TOWER  │  │   🔒 LOCKED     │             ║
║  │                 │  │                 │  │                 │             ║
║  │  🏫📚🎒📚🏫📚🎒  │  │  🏢💼🏢💼🏢💼🏢  │  │  Reach level 10 │             ║
║  │  🏫👥🎒🧟📚🏫🎒  │  │  🏢💼🏢🧟💼🏢🏢  │  │  to unlock this │             ║
║  │  🏫📚🎒📚🏫📚🎒  │  │  🏢💼🏢💼🏢💼🏢  │  │      map        │             ║
║  │                 │  │                 │  │                 │             ║
║  │ Difficulty: ⭐⭐  │  │ Difficulty: ⭐⭐⭐⭐⭐│  │                 │             ║
║  │ Best: 15,200    │  │ Best: 3,100     │  │                 │             ║
║  └─────────────────┘  └─────────────────┘  └─────────────────┘             ║
║                                                                              ║
║                    ┌─────────────┐  ┌─────────────┐                        ║
║                    │   🎮 PLAY   │  │  ⬅️ BACK   │                        ║
║                    └─────────────┘  └─────────────┘                        ║
╚══════════════════════════════════════════════════════════════════════════════╝""",
                    "components": [
                        "Map preview cards with ASCII art",
                        "Difficulty indicators (stars)",
                        "Best score display per map",
                        "Lock status for advanced maps",
                        "Play and back buttons"
                    ],
                    "interactions": [
                        "Hover effects show map details",
                        "Click to select map",
                        "Locked maps show unlock requirements"
                    ]
                },
                {
                    "name": "Game Over Screen",
                    "type": "results",
                    "purpose": "Display final results and next actions",
                    "wireframe": """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                              🧟 GAME OVER 🧟                                ║
║                                                                              ║
║                            ┌─────────────────┐                              ║
║                            │  FINAL SCORE:   │                              ║
║                            │    🏆 24,680    │                              ║
║                            └─────────────────┘                              ║
║                                                                              ║
║  ┌─────────────────────────────────────────────────────────────────────┐   ║
║  │                        📊 STATISTICS                                │   ║
║  │                                                                     │   ║
║  │  🧟 Humans Infected: 127        ⏱️ Survival Time: 4:32            │   ║
║  │  🦠 Chain Reactions: 23         💊 Power-ups Used: 8              │   ║
║  │  🏃 Distance Traveled: 2.1km    🎯 Accuracy: 89%                  │   ║
║  │                                                                     │   ║
║  └─────────────────────────────────────────────────────────────────────┘   ║
║                                                                              ║
║  ┌─────────────────────────────────────────────────────────────────────┐   ║
║  │                      🏆 NEW ACHIEVEMENTS                            │   ║
║  │                                                                     │   ║
║  │  🥇 Speed Demon - Infected 10 humans in 30 seconds                 │   ║
║  │  🦠 Plague Master - Started 5 chain reactions in one game          │   ║
║  │                                                                     │   ║
║  └─────────────────────────────────────────────────────────────────────┘   ║
║                                                                              ║
║                    ┌─────────────┐  ┌─────────────┐                        ║
║                    │ 🔄 PLAY     │  │ 🏠 MAIN     │                        ║
║                    │   AGAIN     │  │   MENU      │                        ║
║                    └─────────────┘  └─────────────┘                        ║
║                                                                              ║
║                              ┌─────────────┐                                ║
║                              │ 📤 SHARE    │                                ║
║                              │   SCORE     │                                ║
║                              └─────────────┘                                ║
╚══════════════════════════════════════════════════════════════════════════════╝""",
                    "components": [
                        "Final score (large, prominent)",
                        "Detailed statistics grid",
                        "New achievements section",
                        "Play again button",
                        "Main menu button",
                        "Share score button"
                    ],
                    "interactions": [
                        "Animated score counting up",
                        "Achievement unlock animations",
                        "Social sharing integration"
                    ]
                }
            ],
            "design_principles": [
                "ASCII art provides clear visual structure",
                "Minimal UI to avoid blocking 3D game view",
                "High contrast for readability during action",
                "Consistent emoji iconography",
                "Game-themed visual elements"
            ],
            "responsive_breakpoints": [
                "Desktop: 1920x1080 (primary target)",
                "Laptop: 1366x768 (scaled UI)",
                "Tablet: 1024x768 (touch-optimized)",
                "Mobile: Limited support (simplified UI)"
            ]
        }
    else:
        # Standard web application wireframes
        return {
            "wireframe_type": "Web Application Wireframes",
            "pages": [
                {
                    "name": "Landing Page",
                    "type": "landing",
                    "purpose": "Main entry point and value proposition",
                    "wireframe": """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║  [LOGO]                    Navigation Menu                    [LOGIN/SIGNUP] ║
║                                                                              ║
║                              HERO SECTION                                   ║
║                                                                              ║
║                         Main Headline Here                                  ║
║                      Supporting text and benefits                           ║
║                                                                              ║
║                         ┌─────────────────┐                                 ║
║                         │   CALL TO ACTION │                                 ║
║                         └─────────────────┘                                 ║
║                                                                              ║
║  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             ║
║  │    FEATURE 1    │  │    FEATURE 2    │  │    FEATURE 3    │             ║
║  │                 │  │                 │  │                 │             ║
║  │  [Icon/Image]   │  │  [Icon/Image]   │  │  [Icon/Image]   │             ║
║  │                 │  │                 │  │                 │             ║
║  │   Description   │  │   Description   │  │   Description   │             ║
║  └─────────────────┘  └─────────────────┘  └─────────────────┘             ║
║                                                                              ║
║                              FOOTER                                         ║
║                    Links | Contact | Social Media                          ║
╚══════════════════════════════════════════════════════════════════════════════╝""",
                    "components": [
                        "Header with logo and navigation",
                        "Hero section with main CTA",
                        "Feature highlights grid",
                        "Footer with links"
                    ],
                    "interactions": [
                        "Smooth scrolling navigation",
                        "Hover effects on features",
                        "Responsive mobile menu"
                    ]
                }
            ],
            "design_principles": [
                "Clean, professional layout",
                "Clear visual hierarchy",
                "Mobile-first responsive design",
                "Accessibility compliance"
            ]
        }


async def generate_design_system(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate comprehensive design system."""
    await asyncio.sleep(1)

    return {
        "design_system_name": "Zombie Game Design System",
        "version": "1.0",
        "color_palette": {
            "primary": {
                "zombie_green": "#4CAF50",
                "infection_red": "#F44336",
                "dark_green": "#2E7D32",
                "bright_green": "#8BC34A"
            },
            "secondary": {
                "human_blue": "#2196F3",
                "warning_orange": "#FF9800",
                "neutral_gray": "#9E9E9E",
                "dark_gray": "#424242"
            },
            "background": {
                "dark_primary": "#1A1A1A",
                "dark_secondary": "#2D2D2D",
                "overlay": "rgba(0, 0, 0, 0.7)"
            },
            "text": {
                "primary": "#FFFFFF",
                "secondary": "#CCCCCC",
                "accent": "#4CAF50"
            }
        },
        "typography": {
            "font_families": {
                "primary": "Orbitron (futuristic, tech feel)",
                "secondary": "Roboto (readable UI text)",
                "accent": "Creepster (horror theme)"
            },
            "font_sizes": {
                "h1": "48px (game title)",
                "h2": "36px (section headers)",
                "h3": "24px (subsections)",
                "body": "16px (UI text)",
                "small": "14px (labels)",
                "caption": "12px (fine print)"
            },
            "font_weights": {
                "light": 300,
                "regular": 400,
                "medium": 500,
                "bold": 700
            }
        },
        "spacing": {
            "base_unit": "8px",
            "scale": [4, 8, 16, 24, 32, 48, 64, 96],
            "component_spacing": {
                "button_padding": "12px 24px",
                "card_padding": "24px",
                "section_margin": "48px"
            }
        },
        "components": {
            "buttons": {
                "primary": {
                    "background": "zombie_green",
                    "text": "white",
                    "border_radius": "8px",
                    "hover_effect": "brightness(110%)",
                    "active_effect": "scale(0.98)"
                },
                "secondary": {
                    "background": "transparent",
                    "text": "zombie_green",
                    "border": "2px solid zombie_green",
                    "hover_effect": "background: zombie_green, text: white"
                },
                "danger": {
                    "background": "infection_red",
                    "text": "white",
                    "hover_effect": "brightness(110%)"
                }
            },
            "cards": {
                "background": "dark_secondary",
                "border_radius": "12px",
                "shadow": "0 4px 12px rgba(0, 0, 0, 0.3)",
                "border": "1px solid rgba(255, 255, 255, 0.1)"
            },
            "progress_bars": {
                "health": "infection_red to zombie_green gradient",
                "energy": "warning_orange to bright_green gradient",
                "loading": "animated zombie_green pulse"
            }
        },
        "animations": {
            "transitions": {
                "fast": "0.15s ease-out",
                "medium": "0.3s ease-in-out",
                "slow": "0.5s ease-in-out"
            },
            "effects": {
                "button_hover": "transform: translateY(-2px)",
                "card_hover": "shadow intensity increase",
                "loading": "pulse animation",
                "success": "green glow effect",
                "error": "red shake animation"
            }
        },
        "iconography": {
            "style": "Outlined with 2px stroke weight",
            "size_scale": [16, 20, 24, 32, 48],
            "zombie_icons": "Custom designed zombie-themed icons",
            "ui_icons": "Material Design icons for standard UI"
        },
        "accessibility": {
            "contrast_ratios": "WCAG AA compliant (4.5:1 minimum)",
            "focus_indicators": "2px zombie_green outline",
            "colorblind_support": "Icons and patterns supplement color coding",
            "keyboard_navigation": "Full keyboard accessibility"
        }
    }


async def generate_file_structure(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate project file structure."""
    await asyncio.sleep(1)

    return {
        "project_structure": {
            "root": {
                "package.json": "Project dependencies and scripts",
                "tsconfig.json": "TypeScript configuration",
                "vite.config.ts": "Build tool configuration",
                "README.md": "Project documentation",
                ".gitignore": "Git ignore rules",
                ".env.example": "Environment variables template"
            },
            "src/": {
                "main.ts": "Application entry point",
                "App.tsx": "Root React component",
                "index.html": "HTML template",
                "game/": {
                    "Game.ts": "Main game class",
                    "Scene.ts": "Three.js scene setup",
                    "Camera.ts": "Camera controls",
                    "Renderer.ts": "WebGL renderer setup",
                    "entities/": {
                        "Zombie.ts": "Zombie character class",
                        "Human.ts": "Human NPC class",
                        "Entity.ts": "Base entity class"
                    },
                    "systems/": {
                        "InfectionSystem.ts": "Infection mechanics",
                        "AISystem.ts": "NPC artificial intelligence",
                        "PhysicsSystem.ts": "Collision detection",
                        "InputSystem.ts": "Player input handling",
                        "AudioSystem.ts": "3D spatial audio"
                    },
                    "components/": {
                        "Transform.ts": "Position, rotation, scale",
                        "Mesh.ts": "3D model component",
                        "Collider.ts": "Collision detection",
                        "Health.ts": "Health/infection status",
                        "AI.ts": "AI behavior component"
                    }
                },
                "ui/": {
                    "components/": {
                        "HUD.tsx": "Game heads-up display",
                        "MainMenu.tsx": "Main menu screen",
                        "GameOver.tsx": "Game over screen",
                        "Settings.tsx": "Settings panel"
                    },
                    "hooks/": {
                        "useGameState.ts": "Game state management",
                        "useAudio.ts": "Audio controls",
                        "useInput.ts": "Input handling"
                    }
                },
                "assets/": {
                    "models/": {
                        "zombie.glb": "Zombie 3D model",
                        "human.glb": "Human 3D model",
                        "buildings/": "Environment models"
                    },
                    "textures/": {
                        "zombie_diffuse.jpg": "Zombie texture",
                        "environment/": "Environment textures"
                    },
                    "audio/": {
                        "sfx/": "Sound effects",
                        "music/": "Background music"
                    }
                },
                "utils/": {
                    "math.ts": "Mathematical utilities",
                    "loader.ts": "Asset loading",
                    "performance.ts": "Performance monitoring"
                },
                "types/": {
                    "game.ts": "Game-related type definitions",
                    "entities.ts": "Entity type definitions"
                }
            },
            "public/": {
                "favicon.ico": "Website icon",
                "manifest.json": "PWA manifest"
            },
            "tests/": {
                "unit/": "Unit tests",
                "integration/": "Integration tests",
                "e2e/": "End-to-end tests"
            },
            "docs/": {
                "API.md": "API documentation",
                "DEPLOYMENT.md": "Deployment guide",
                "CONTRIBUTING.md": "Contribution guidelines"
            }
        },
        "file_naming_conventions": [
            "PascalCase for classes and components",
            "camelCase for functions and variables",
            "kebab-case for file names",
            "UPPER_CASE for constants"
        ],
        "folder_organization": [
            "Group by feature/domain (game, ui, utils)",
            "Separate concerns (components, systems, entities)",
            "Keep related files together",
            "Use index files for clean imports"
        ]
    }


async def generate_development_workflow(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate development workflow and processes."""
    await asyncio.sleep(1)

    return {
        "development_methodology": "Agile with 2-week sprints",
        "version_control": {
            "system": "Git with GitHub",
            "branching_strategy": "GitFlow",
            "branch_types": {
                "main": "Production-ready code",
                "develop": "Integration branch for features",
                "feature/*": "Individual feature development",
                "release/*": "Release preparation",
                "hotfix/*": "Critical bug fixes"
            },
            "commit_conventions": "Conventional Commits (feat, fix, docs, style, refactor, test, chore)"
        },
        "development_phases": [
            {
                "phase": "Phase 1: Core Foundation",
                "duration": "3-4 weeks",
                "deliverables": [
                    "Basic 3D scene setup",
                    "Zombie character controller",
                    "Simple human NPCs",
                    "Basic infection mechanic"
                ],
                "success_criteria": [
                    "Playable prototype",
                    "60fps performance",
                    "Basic gameplay loop"
                ]
            },
            {
                "phase": "Phase 2: Game Systems",
                "duration": "3-4 weeks",
                "deliverables": [
                    "Advanced AI behaviors",
                    "Multiple zombie types",
                    "Power-up system",
                    "Chain reaction mechanics"
                ],
                "success_criteria": [
                    "Engaging gameplay",
                    "Balanced difficulty",
                    "Stable performance with 100+ NPCs"
                ]
            },
            {
                "phase": "Phase 3: Polish & Launch",
                "duration": "2-3 weeks",
                "deliverables": [
                    "Multiple maps",
                    "UI/UX polish",
                    "Audio integration",
                    "Performance optimization"
                ],
                "success_criteria": [
                    "Production-ready quality",
                    "Cross-browser compatibility",
                    "Comprehensive testing"
                ]
            }
        ],
        "quality_assurance": {
            "testing_strategy": [
                "Unit tests for game logic",
                "Integration tests for systems",
                "Performance testing",
                "Cross-browser testing",
                "User acceptance testing"
            ],
            "code_review_process": [
                "All code requires peer review",
                "Automated checks (linting, tests)",
                "Performance impact assessment",
                "Security review for user data"
            ],
            "performance_monitoring": [
                "FPS tracking in development",
                "Memory usage profiling",
                "Bundle size monitoring",
                "Load time optimization"
            ]
        },
        "deployment_pipeline": {
            "environments": [
                "Development (local)",
                "Staging (preview deployments)",
                "Production (live site)"
            ],
            "ci_cd": [
                "Automated testing on PR",
                "Build verification",
                "Deployment to staging",
                "Manual approval for production"
            ],
            "monitoring": [
                "Error tracking (Sentry)",
                "Performance monitoring",
                "User analytics",
                "Server health checks"
            ]
        },
        "team_collaboration": {
            "communication_tools": [
                "Slack/Discord for daily communication",
                "GitHub for code collaboration",
                "Figma for design collaboration",
                "Notion for documentation"
            ],
            "meeting_schedule": [
                "Daily standups (15 min)",
                "Sprint planning (2 hours)",
                "Sprint review (1 hour)",
                "Retrospective (1 hour)"
            ],
            "documentation_requirements": [
                "Code comments for complex logic",
                "API documentation",
                "Architecture decisions",
                "User guides"
            ]
        }
    }


async def generate_project_scaffold(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Generate project scaffolding and initial setup."""
    await asyncio.sleep(1)

    return {
        "scaffolding_approach": "Vite + React + TypeScript + Three.js template",
        "initial_setup_commands": [
            "npm create vite@latest zombie-game -- --template react-ts",
            "cd zombie-game",
            "npm install",
            "npm install three @types/three",
            "npm install cannon-es howler.js",
            "npm install -D @typescript-eslint/eslint-plugin prettier"
        ],
        "package_json_scripts": {
            "dev": "vite",
            "build": "tsc && vite build",
            "preview": "vite preview",
            "test": "jest",
            "test:watch": "jest --watch",
            "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
            "lint:fix": "eslint src --ext ts,tsx --fix",
            "format": "prettier --write src/**/*.{ts,tsx}",
            "type-check": "tsc --noEmit"
        },
        "essential_dependencies": {
            "production": {
                "three": "^0.158.0",
                "cannon-es": "^0.20.0",
                "howler": "^2.2.3",
                "react": "^18.2.0",
                "react-dom": "^18.2.0"
            },
            "development": {
                "@types/three": "^0.158.0",
                "@typescript-eslint/eslint-plugin": "^6.0.0",
                "eslint": "^8.45.0",
                "prettier": "^3.0.0",
                "typescript": "^5.0.2",
                "vite": "^4.4.5"
            }
        },
        "configuration_files": {
            "tsconfig.json": {
                "compilerOptions": {
                    "target": "ES2020",
                    "lib": ["ES2020", "DOM", "DOM.Iterable"],
                    "module": "ESNext",
                    "skipLibCheck": True,
                    "moduleResolution": "bundler",
                    "allowImportingTsExtensions": True,
                    "resolveJsonModule": True,
                    "isolatedModules": True,
                    "noEmit": True,
                    "jsx": "react-jsx",
                    "strict": True,
                    "noUnusedLocals": True,
                    "noUnusedParameters": True,
                    "noFallthroughCasesInSwitch": True
                }
            },
            "vite.config.ts": {
                "plugins": ["react()"],
                "server": {"port": 3000},
                "build": {
                    "target": "esnext",
                    "minify": "terser",
                    "rollupOptions": {
                        "output": {
                            "manualChunks": {
                                "three": ["three"],
                                "physics": ["cannon-es"],
                                "audio": ["howler"]
                            }
                        }
                    }
                }
            }
        },
        "initial_file_templates": {
            "src/game/Game.ts": "Main game class with initialization and game loop",
            "src/game/Scene.ts": "Three.js scene setup with lighting and environment",
            "src/game/entities/Zombie.ts": "Player-controlled zombie character",
            "src/game/entities/Human.ts": "AI-controlled human NPCs",
            "src/game/systems/InfectionSystem.ts": "Core infection mechanics",
            "src/ui/components/HUD.tsx": "Game UI overlay component"
        },
        "development_server_setup": [
            "Hot module replacement for fast development",
            "TypeScript compilation",
            "ESLint integration",
            "Prettier formatting",
            "Source maps for debugging"
        ],
        "build_optimization": [
            "Tree shaking for smaller bundles",
            "Code splitting by feature",
            "Asset optimization",
            "Gzip compression",
            "Browser caching strategies"
        ]
    }


async def ai_generate_detailed_tasks(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """AI-powered comprehensive task breakdown generation."""

    analysis = context.get("analyze", {})
    summary = context.get("summary", {})
    techstack = context.get("techstack", {})

    tasks_prompt = f"""You are an expert project manager and software architect. Create a comprehensive task breakdown for this project with detailed specifications.

Project Description: {prompt}

Project Analysis:
- Type: {analysis.get('project_type', 'unknown')}
- Complexity: {analysis.get('complexity', 'unknown')}
- Features: {', '.join(analysis.get('features', []))}
- Timeline: {analysis.get('estimated_timeline', 'unknown')}
- Team Size: {analysis.get('team_size_recommendation', 'unknown')}

Tech Stack:
- Frontend: {techstack.get('frontend', {}).get('primary', 'unknown')}
- Backend: {techstack.get('backend', {}).get('primary', 'unknown')}
- Database: {techstack.get('database', {}).get('primary', 'unknown')}

Generate detailed tasks in JSON format:

{{
    "tasks": [
        {{
            "id": "CATEGORY-001",
            "title": "Specific task title",
            "description": "Detailed task description",
            "category": "setup/frontend/backend/testing/deployment",
            "priority": "critical/high/medium/low",
            "estimated_hours": "realistic hour estimate",
            "dependencies": ["list of task IDs this depends on"],
            "subtasks": [
                "Specific subtask 1",
                "Specific subtask 2",
                "etc (4-8 subtasks)"
            ],
            "acceptance_criteria": [
                "Measurable completion criteria 1",
                "Measurable completion criteria 2",
                "etc (3-5 criteria)"
            ],
            "technical_notes": "Implementation guidance and considerations",
            "deliverables": ["specific outputs/artifacts"],
            "skills_required": ["required technical skills"]
        }}
    ],
    "total_estimated_hours": "sum of all task hours",
    "task_categories": ["unique list of categories"],
    "estimated_timeline": "realistic timeline based on hours and team size",
    "team_size_recommendation": "recommended team composition",
    "critical_path": ["sequence of dependent tasks"],
    "risk_factors": ["potential project risks"],
    "success_metrics": ["measurable success criteria"],
    "milestones": [
        {{
            "name": "milestone name",
            "week": "target week",
            "deliverables": ["key deliverables"],
            "success_criteria": ["completion criteria"]
        }}
    ]
}}

Requirements:
1. Create 8-15 detailed tasks based on project complexity
2. Include realistic hour estimates (consider team experience)
3. Ensure proper task dependencies and sequencing
4. Include specific, actionable subtasks
5. Provide measurable acceptance criteria
6. Consider the specific technology stack chosen
7. Include setup, development, testing, and deployment phases
8. Account for project-specific requirements (3D graphics, AI, etc.)

Return only JSON with comprehensive task breakdown."""

    messages = [
        {"role": "user", "content": tasks_prompt}
    ]

    try:
        response = await ai_client.generate_completion(messages)
        return json.loads(response)
    except Exception as e:
        logger.error("AI task generation failed", error=str(e))
        return await fallback_generate_detailed_tasks(prompt, context)


async def fallback_generate_detailed_tasks(prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Fallback task generation when AI fails."""
    await asyncio.sleep(1)

    analysis = context.get("analyze", {})
    project_type = analysis.get("project_type", "web_application")
    features = analysis.get("features", [])
    complexity = analysis.get("complexity", "medium")

    if project_type == "game_application":
        tasks = [
            {
                "id": "SETUP-001",
                "title": "Project Setup & Environment Configuration",
                "description": "Initialize project structure, dependencies, and development environment",
                "category": "setup",
                "priority": "critical",
                "estimated_hours": 8,
                "dependencies": [],
                "subtasks": [
                    "Create Vite + React + TypeScript project",
                    "Install Three.js and physics engine (Cannon.js)",
                    "Configure ESLint, Prettier, and TypeScript",
                    "Set up development server and hot reloading",
                    "Create initial project structure and folders",
                    "Configure build optimization and bundling"
                ],
                "acceptance_criteria": [
                    "Development server runs without errors",
                    "TypeScript compilation works correctly",
                    "Linting and formatting rules are enforced",
                    "Basic Three.js scene renders successfully"
                ],
                "technical_notes": "Use Vite for fast development and optimized builds"
            },
            {
                "id": "GRAPHICS-001",
                "title": "3D Scene Foundation",
                "description": "Set up Three.js scene, camera, lighting, and basic 3D environment",
                "category": "graphics",
                "priority": "critical",
                "estimated_hours": 16,
                "dependencies": ["SETUP-001"],
                "subtasks": [
                    "Initialize Three.js scene, camera, and renderer",
                    "Implement camera controls (first-person perspective)",
                    "Set up lighting system (ambient, directional, point lights)",
                    "Create basic environment (ground, buildings, obstacles)",
                    "Implement shadow mapping and basic post-processing",
                    "Add fog and atmospheric effects"
                ],
                "acceptance_criteria": [
                    "3D scene renders at 60fps on target hardware",
                    "Camera controls are smooth and responsive",
                    "Lighting creates realistic atmosphere",
                    "Environment provides interesting gameplay space"
                ],
                "technical_notes": "Use WebGL 2.0 features for better performance"
            },
            {
                "id": "PLAYER-001",
                "title": "Zombie Character Controller",
                "description": "Implement player-controlled zombie character with movement and abilities",
                "category": "gameplay",
                "priority": "critical",
                "estimated_hours": 20,
                "dependencies": ["GRAPHICS-001"],
                "subtasks": [
                    "Create zombie 3D model and animations",
                    "Implement WASD movement with mouse look",
                    "Add collision detection with environment",
                    "Create infection ability with visual effects",
                    "Implement different zombie types and abilities",
                    "Add sound effects for movement and actions"
                ],
                "acceptance_criteria": [
                    "Character responds to input within 50ms",
                    "Movement feels natural and fluid",
                    "Collision detection prevents wall clipping",
                    "Infection ability has clear visual feedback"
                ],
                "technical_notes": "Use state machine for character behavior management"
            },
            {
                "id": "AI-001",
                "title": "Human NPC AI System",
                "description": "Develop intelligent human NPCs with survival behaviors and pathfinding",
                "category": "ai",
                "priority": "critical",
                "estimated_hours": 24,
                "dependencies": ["PLAYER-001"],
                "subtasks": [
                    "Implement A* pathfinding algorithm",
                    "Create behavior tree system for AI decisions",
                    "Add fear and panic response mechanics",
                    "Implement group behavior and communication",
                    "Create different human types (civilian, security, etc.)",
                    "Optimize AI performance for 100+ NPCs"
                ],
                "acceptance_criteria": [
                    "NPCs navigate environment intelligently",
                    "Realistic fear responses to zombie presence",
                    "Group behaviors emerge naturally",
                    "Performance remains stable with 100+ NPCs"
                ],
                "technical_notes": "Use spatial partitioning for efficient NPC management"
            },
            {
                "id": "INFECTION-001",
                "title": "Infection Mechanics System",
                "description": "Implement core infection spreading and chain reaction mechanics",
                "category": "gameplay",
                "priority": "critical",
                "estimated_hours": 18,
                "dependencies": ["AI-001"],
                "subtasks": [
                    "Create infection spreading algorithm",
                    "Implement visual transformation animations",
                    "Add chain reaction mechanics",
                    "Create infection progress tracking",
                    "Implement different infection types",
                    "Add audio feedback for infections"
                ],
                "acceptance_criteria": [
                    "Infection spreads realistically on contact",
                    "Visual transformation is satisfying",
                    "Chain reactions create emergent gameplay",
                    "Infection progress is clearly communicated"
                ],
                "technical_notes": "Use event system for infection state changes"
            },
            {
                "id": "PHYSICS-001",
                "title": "Physics Engine Integration",
                "description": "Add realistic collision detection and physics simulation",
                "category": "physics",
                "priority": "high",
                "estimated_hours": 14,
                "dependencies": ["INFECTION-001"],
                "subtasks": [
                    "Integrate Cannon.js physics engine",
                    "Set up collision detection for all entities",
                    "Implement realistic gravity and movement",
                    "Add physics-based interactions",
                    "Optimize physics performance",
                    "Create physics debugging tools"
                ],
                "acceptance_criteria": [
                    "Collision detection is accurate and responsive",
                    "Physics simulation feels realistic",
                    "Performance impact is minimal",
                    "No physics glitches or edge cases"
                ],
                "technical_notes": "Use simplified collision shapes for performance"
            },
            {
                "id": "POWERUPS-001",
                "title": "Power-up System Implementation",
                "description": "Create power-ups and special abilities for zombie enhancement",
                "category": "gameplay",
                "priority": "high",
                "estimated_hours": 12,
                "dependencies": ["PHYSICS-001"],
                "subtasks": [
                    "Design power-up types and effects",
                    "Implement power-up spawning system",
                    "Create visual effects for power-ups",
                    "Add temporary ability modifications",
                    "Implement cooldown and duration systems",
                    "Balance power-up effects"
                ],
                "acceptance_criteria": [
                    "Power-ups provide meaningful gameplay benefits",
                    "Visual effects clearly communicate power-up status",
                    "System is balanced and not overpowered",
                    "Power-ups encourage strategic gameplay"
                ],
                "technical_notes": "Use component system for modular power-up effects"
            },
            {
                "id": "UI-001",
                "title": "User Interface Development",
                "description": "Create game UI including HUD, menus, and interactive elements",
                "category": "ui",
                "priority": "high",
                "estimated_hours": 16,
                "dependencies": ["POWERUPS-001"],
                "subtasks": [
                    "Design and implement main menu",
                    "Create in-game HUD with health, score, mini-map",
                    "Build settings and options screens",
                    "Implement game over and results screens",
                    "Add responsive design for different screen sizes",
                    "Create accessibility features"
                ],
                "acceptance_criteria": [
                    "UI is intuitive and easy to navigate",
                    "HUD provides essential information without clutter",
                    "Responsive design works on various devices",
                    "Accessibility guidelines are followed"
                ],
                "technical_notes": "Use React components with game state integration"
            },
            {
                "id": "AUDIO-001",
                "title": "Audio System Integration",
                "description": "Implement 3D spatial audio, sound effects, and background music",
                "category": "audio",
                "priority": "medium",
                "estimated_hours": 10,
                "dependencies": ["UI-001"],
                "subtasks": [
                    "Set up Howler.js for audio management",
                    "Implement 3D spatial audio positioning",
                    "Add sound effects for all game actions",
                    "Create dynamic background music system",
                    "Implement audio settings and controls",
                    "Optimize audio performance and loading"
                ],
                "acceptance_criteria": [
                    "3D audio enhances spatial awareness",
                    "Sound effects provide clear feedback",
                    "Music adapts to gameplay intensity",
                    "Audio settings work correctly"
                ],
                "technical_notes": "Use Web Audio API for advanced audio features"
            },
            {
                "id": "OPTIMIZATION-001",
                "title": "Performance Optimization",
                "description": "Optimize graphics, physics, and overall game performance",
                "category": "optimization",
                "priority": "high",
                "estimated_hours": 14,
                "dependencies": ["AUDIO-001"],
                "subtasks": [
                    "Implement Level of Detail (LOD) system",
                    "Optimize 3D model complexity and textures",
                    "Add object pooling for entities",
                    "Implement frustum culling and occlusion",
                    "Optimize AI and physics calculations",
                    "Add performance monitoring and profiling"
                ],
                "acceptance_criteria": [
                    "Maintains 60fps with 200+ entities",
                    "Memory usage stays under 2GB",
                    "Loading times are under 10 seconds",
                    "Performance is consistent across target devices"
                ],
                "technical_notes": "Use browser dev tools for performance profiling"
            },
            {
                "id": "TESTING-001",
                "title": "Testing & Quality Assurance",
                "description": "Comprehensive testing including unit, integration, and user testing",
                "category": "testing",
                "priority": "high",
                "estimated_hours": 16,
                "dependencies": ["OPTIMIZATION-001"],
                "subtasks": [
                    "Write unit tests for game logic",
                    "Create integration tests for systems",
                    "Perform cross-browser compatibility testing",
                    "Conduct performance testing on various devices",
                    "Execute user acceptance testing",
                    "Fix bugs and issues found during testing"
                ],
                "acceptance_criteria": [
                    "All critical bugs are resolved",
                    "Game works on all target browsers",
                    "Performance meets requirements",
                    "User feedback is positive"
                ],
                "technical_notes": "Use Jest for unit testing and Cypress for E2E testing"
            },
            {
                "id": "DEPLOYMENT-001",
                "title": "Deployment & Launch Preparation",
                "description": "Deploy to production and prepare for public launch",
                "category": "deployment",
                "priority": "medium",
                "estimated_hours": 8,
                "dependencies": ["TESTING-001"],
                "subtasks": [
                    "Set up production build pipeline",
                    "Configure CDN for asset delivery",
                    "Implement analytics and error tracking",
                    "Create deployment documentation",
                    "Set up monitoring and alerts",
                    "Prepare launch marketing materials"
                ],
                "acceptance_criteria": [
                    "Production deployment is stable",
                    "CDN delivers assets efficiently",
                    "Monitoring systems are operational",
                    "Documentation is complete"
                ],
                "technical_notes": "Use Vercel or Netlify for easy deployment"
            }
        ]
    else:
        # Fallback for non-game projects
        tasks = [
            {
                "id": "SETUP-001",
                "title": "Project Setup",
                "description": "Initialize project structure and dependencies",
                "category": "setup",
                "priority": "high",
                "estimated_hours": 4
            }
        ]

    total_hours = sum(task["estimated_hours"] for task in tasks)
    categories = list(set(task["category"] for task in tasks))

    return {
        "tasks": tasks,
        "total_estimated_hours": total_hours,
        "task_categories": categories,
        "estimated_timeline": f"{total_hours // 40 + 1}-{total_hours // 30 + 2} weeks",
        "team_size_recommendation": "4-6 developers" if complexity == "high" else "3-4 developers",
        "critical_path": [
            "SETUP-001 → GRAPHICS-001 → PLAYER-001 → AI-001 → INFECTION-001",
            "Parallel: PHYSICS-001, POWERUPS-001, UI-001, AUDIO-001",
            "Final: OPTIMIZATION-001 → TESTING-001 → DEPLOYMENT-001"
        ],
        "risk_factors": [
            "3D performance optimization complexity",
            "AI system scalability challenges",
            "Cross-browser WebGL compatibility",
            "Asset loading and memory management"
        ],
        "success_metrics": [
            "60fps performance on target hardware",
            "< 10 second loading time",
            "Player retention > 70%",
            "Crash rate < 1%"
        ]
    }

# API Routes
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "0.1.0",
        "service": "AG3NT Backend (Simplified)",
    }

@app.get("/api")
async def api_root():
    """API root endpoint."""
    return {
        "message": "AG3NT Backend API (Simplified)",
        "version": "v1",
        "endpoints": {
            "projects": "/api/v1/projects",
            "health": "/health",
        }
    }

@app.options("/api/v1/projects")
async def options_projects():
    """Handle OPTIONS preflight for projects endpoint."""
    return {"message": "OK"}

@app.options("/api/v1/projects/{project_id}/steps")
async def options_project_steps(project_id: str):
    """Handle OPTIONS preflight for project steps endpoint."""
    return {"message": "OK"}

@app.post("/api/v1/projects", response_model=ProjectResponse)
async def create_project(
    request: ProjectCreateRequest,
    background_tasks: BackgroundTasks
) -> ProjectResponse:
    """Create a new project."""
    try:
        project_id = str(uuid4())
        now = datetime.now()
        
        project = {
            "project_id": project_id,
            "status": "initializing",
            "progress": 0.0,
            "user_prompt": request.prompt,
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "context": {
                "is_interactive": request.is_interactive,
                "user_answers": request.answers,
                "design_style_guide": request.design_style_guide,
                "has_images": request.has_images,
                "current_step": "initialize",
                "completed_steps": [],
                "results": {},
            },
            "error_message": None,
        }
        
        projects_store[project_id] = project
        
        logger.info("Project created", project_id=project_id)
        
        return ProjectResponse(
            project_id=project_id,
            status="initializing",
            progress=0.0,
            user_prompt=request.prompt,
            created_at=now.isoformat(),
            updated_at=now.isoformat(),
            results={},
        )
        
    except Exception as e:
        logger.error("Failed to create project", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/projects/{project_id}", response_model=ProjectResponse)
async def get_project(project_id: str) -> ProjectResponse:
    """Get project by ID."""
    if project_id not in projects_store:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    
    project = projects_store[project_id]
    
    return ProjectResponse(
        project_id=project["project_id"],
        status=project["status"],
        progress=project["progress"],
        user_prompt=project["user_prompt"],
        created_at=project["created_at"],
        updated_at=project["updated_at"],
        results=project["context"].get("results", {}),
        error_message=project.get("error_message"),
    )

@app.post("/api/v1/projects/{project_id}/steps", response_model=ProjectStepResponse)
async def execute_project_step(
    project_id: str,
    request: ProjectStepRequest,
    background_tasks: BackgroundTasks
) -> ProjectStepResponse:
    """Execute a project step."""
    if project_id not in projects_store:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    
    project = projects_store[project_id]
    
    try:
        logger.info("Executing step", project_id=project_id, step=request.step)
        
        # AI-powered step execution with detailed content generation
        if request.step == "analyze":
            result = await ai_analyze_project(project["user_prompt"])
            project["status"] = "planning"
            project["progress"] = 0.1
        elif request.step == "clarify":
            analysis = project["context"]["results"].get("analyze", {})
            result = await ai_generate_clarification_questions(project["user_prompt"], analysis)
            project["progress"] = 0.2
        elif request.step == "summary":
            analysis = project["context"]["results"].get("analyze", {})
            clarify = project["context"]["results"].get("clarify", {})
            result = await generate_project_summary(project["user_prompt"], analysis, clarify)
            project["progress"] = 0.3
        elif request.step == "techstack":
            analysis = project["context"]["results"].get("analyze", {})
            summary = project["context"]["results"].get("summary", {})
            result = await generate_tech_stack(project["user_prompt"], analysis, summary)
            project["progress"] = 0.4
        elif request.step == "prd":
            result = await generate_prd(project["user_prompt"], project["context"]["results"])
            project["progress"] = 0.5
        elif request.step == "database":
            result = await generate_database_design(project["user_prompt"], project["context"]["results"])
            project["progress"] = 0.6
        elif request.step == "wireframes":
            result = await ai_generate_wireframes(project["user_prompt"], project["context"]["results"])
            project["progress"] = 0.7
        elif request.step == "design":
            result = await generate_design_system(project["user_prompt"], project["context"]["results"])
            project["progress"] = 0.8
        elif request.step == "filesystem":
            result = await generate_file_structure(project["user_prompt"], project["context"]["results"])
            project["progress"] = 0.85
        elif request.step == "workflow":
            result = await generate_development_workflow(project["user_prompt"], project["context"]["results"])
            project["progress"] = 0.9
        elif request.step == "scaffold":
            result = await generate_project_scaffold(project["user_prompt"], project["context"]["results"])
            project["progress"] = 0.95
        elif request.step == "tasks":
            result = await ai_generate_detailed_tasks(project["user_prompt"], project["context"]["results"])
            project["status"] = "completed"
            project["progress"] = 1.0
        else:
            result = {"message": f"Step {request.step} completed", "step": request.step}
            project["progress"] = min(1.0, project["progress"] + 0.05)
        
        # Update project
        project["context"]["results"][request.step] = result
        project["context"]["completed_steps"].append(request.step)
        project["context"]["current_step"] = request.step
        project["updated_at"] = datetime.now().isoformat()
        
        logger.info("Step completed", project_id=project_id, step=request.step)
        
        return ProjectStepResponse(
            project_id=project_id,
            step=request.step,
            status="completed",
            result=result,
            needs_input=False,
            question=None,
            next_step=None,
        )
        
    except Exception as e:
        logger.error("Step execution failed", project_id=project_id, step=request.step, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/projects/{project_id}/status")
async def get_project_status(project_id: str):
    """Get project status."""
    if project_id not in projects_store:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    
    project = projects_store[project_id]
    
    return {
        "project_id": project_id,
        "status": project["status"],
        "progress": project["progress"],
        "current_step": project["context"].get("current_step"),
        "completed_steps": project["context"].get("completed_steps", []),
        "error_message": project.get("error_message"),
        "updated_at": project["updated_at"],
    }

if __name__ == "__main__":
    print("🚀 Starting AG3NT Backend (Simplified)...")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Health Check: http://localhost:8000/health")
    print("📊 API Root: http://localhost:8000/api")
    print()
    
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
