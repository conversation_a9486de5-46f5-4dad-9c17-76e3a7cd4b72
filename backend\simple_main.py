#!/usr/bin/env python3
"""
Simplified AG3NT Backend - Basic FastAPI implementation

This is a simplified version that works without CrewAI and LangGraph dependencies.
It provides the same API interface but uses basic implementations.
"""

import sys
import os
import json
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import uuid4

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import structlog

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="AG3NT Backend (Simplified)",
    version="0.1.0",
    description="Simplified multi-agent system backend",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://localhost:3002",
        "http://localhost:3003",
        "http://localhost:3004",
        "http://localhost:3005"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# In-memory storage (replace with database in production)
projects_store: Dict[str, Dict[str, Any]] = {}

# Request/Response models
class ProjectCreateRequest(BaseModel):
    prompt: str
    is_interactive: bool = True
    answers: Dict[str, Any] = Field(default_factory=dict)
    design_style_guide: Optional[Dict[str, Any]] = None
    has_images: bool = False

class ProjectResponse(BaseModel):
    project_id: str
    status: str
    progress: float
    user_prompt: str
    created_at: str
    updated_at: str
    results: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None

class ProjectStepRequest(BaseModel):
    step: str
    context: Dict[str, Any] = Field(default_factory=dict)
    answer: Optional[str] = None

class ProjectStepResponse(BaseModel):
    project_id: str
    step: str
    status: str
    result: Dict[str, Any]
    needs_input: bool = False
    question: Optional[str] = None
    next_step: Optional[str] = None

# Simple mock implementations
async def mock_analyze_project(prompt: str) -> Dict[str, Any]:
    """Mock project analysis."""
    await asyncio.sleep(1)  # Simulate processing time
    
    # Simple keyword-based analysis
    prompt_lower = prompt.lower()
    
    project_type = "web_application"
    if "mobile" in prompt_lower or "app" in prompt_lower:
        project_type = "mobile_application"
    elif "api" in prompt_lower or "backend" in prompt_lower:
        project_type = "backend_service"
    
    features = []
    if "auth" in prompt_lower or "login" in prompt_lower:
        features.append("authentication")
    if "database" in prompt_lower or "data" in prompt_lower:
        features.append("data_management")
    if "payment" in prompt_lower:
        features.append("payment_processing")
    
    complexity = "medium"
    if len(features) > 3 or "complex" in prompt_lower:
        complexity = "high"
    elif len(features) < 2:
        complexity = "low"
    
    return {
        "project_type": project_type,
        "features": features,
        "complexity": complexity,
        "domain": "general",
        "technical_hints": ["Consider modern frameworks", "Plan for scalability"]
    }

async def mock_generate_tasks(summary: Dict[str, Any]) -> Dict[str, Any]:
    """Mock task generation."""
    await asyncio.sleep(1)
    
    tasks = [
        {
            "id": "task_1",
            "title": "Project Setup",
            "description": "Initialize project structure and dependencies",
            "category": "setup",
            "priority": "high",
            "estimated_hours": 4,
        },
        {
            "id": "task_2", 
            "title": "Core Features Implementation",
            "description": "Implement main application features",
            "category": "feature",
            "priority": "high",
            "estimated_hours": 16,
        },
        {
            "id": "task_3",
            "title": "Testing and Deployment",
            "description": "Add tests and deploy to production",
            "category": "testing",
            "priority": "medium",
            "estimated_hours": 8,
        }
    ]
    
    return {
        "tasks": tasks,
        "total_estimated_hours": sum(task["estimated_hours"] for task in tasks),
        "task_categories": ["setup", "feature", "testing"]
    }

# API Routes
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "0.1.0",
        "service": "AG3NT Backend (Simplified)",
    }

@app.get("/api")
async def api_root():
    """API root endpoint."""
    return {
        "message": "AG3NT Backend API (Simplified)",
        "version": "v1",
        "endpoints": {
            "projects": "/api/v1/projects",
            "health": "/health",
        }
    }

@app.post("/api/v1/projects", response_model=ProjectResponse)
async def create_project(
    request: ProjectCreateRequest,
    background_tasks: BackgroundTasks
) -> ProjectResponse:
    """Create a new project."""
    try:
        project_id = str(uuid4())
        now = datetime.now()
        
        project = {
            "project_id": project_id,
            "status": "initializing",
            "progress": 0.0,
            "user_prompt": request.prompt,
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "context": {
                "is_interactive": request.is_interactive,
                "user_answers": request.answers,
                "design_style_guide": request.design_style_guide,
                "has_images": request.has_images,
                "current_step": "initialize",
                "completed_steps": [],
                "results": {},
            },
            "error_message": None,
        }
        
        projects_store[project_id] = project
        
        logger.info("Project created", project_id=project_id)
        
        return ProjectResponse(
            project_id=project_id,
            status="initializing",
            progress=0.0,
            user_prompt=request.prompt,
            created_at=now.isoformat(),
            updated_at=now.isoformat(),
            results={},
        )
        
    except Exception as e:
        logger.error("Failed to create project", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/projects/{project_id}", response_model=ProjectResponse)
async def get_project(project_id: str) -> ProjectResponse:
    """Get project by ID."""
    if project_id not in projects_store:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    
    project = projects_store[project_id]
    
    return ProjectResponse(
        project_id=project["project_id"],
        status=project["status"],
        progress=project["progress"],
        user_prompt=project["user_prompt"],
        created_at=project["created_at"],
        updated_at=project["updated_at"],
        results=project["context"].get("results", {}),
        error_message=project.get("error_message"),
    )

@app.post("/api/v1/projects/{project_id}/steps", response_model=ProjectStepResponse)
async def execute_project_step(
    project_id: str,
    request: ProjectStepRequest,
    background_tasks: BackgroundTasks
) -> ProjectStepResponse:
    """Execute a project step."""
    if project_id not in projects_store:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    
    project = projects_store[project_id]
    
    try:
        logger.info("Executing step", project_id=project_id, step=request.step)
        
        # Mock step execution
        if request.step == "analyze":
            result = await mock_analyze_project(project["user_prompt"])
            project["status"] = "planning"
            project["progress"] = 0.2
        elif request.step == "clarify":
            result = {"questions": []}  # No questions for simplified version
            project["progress"] = 0.4
        elif request.step == "summary":
            result = {
                "project_overview": f"A {project['context']['results'].get('analyze', {}).get('project_type', 'application')} project",
                "key_features": project["context"]["results"].get("analyze", {}).get("features", []),
                "technical_requirements": ["Modern web technologies", "Responsive design"],
            }
            project["progress"] = 0.6
        elif request.step == "techstack":
            result = {
                "frontend": "React",
                "backend": "Node.js",
                "database": "PostgreSQL",
                "deployment": "Vercel",
            }
            project["progress"] = 0.8
        elif request.step == "tasks":
            summary = project["context"]["results"].get("summary", {})
            result = await mock_generate_tasks(summary)
            project["status"] = "completed"
            project["progress"] = 1.0
        else:
            result = {"message": f"Step {request.step} completed", "step": request.step}
            project["progress"] = min(1.0, project["progress"] + 0.1)
        
        # Update project
        project["context"]["results"][request.step] = result
        project["context"]["completed_steps"].append(request.step)
        project["context"]["current_step"] = request.step
        project["updated_at"] = datetime.now().isoformat()
        
        logger.info("Step completed", project_id=project_id, step=request.step)
        
        return ProjectStepResponse(
            project_id=project_id,
            step=request.step,
            status="completed",
            result=result,
            needs_input=False,
            question=None,
            next_step=None,
        )
        
    except Exception as e:
        logger.error("Step execution failed", project_id=project_id, step=request.step, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/projects/{project_id}/status")
async def get_project_status(project_id: str):
    """Get project status."""
    if project_id not in projects_store:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")
    
    project = projects_store[project_id]
    
    return {
        "project_id": project_id,
        "status": project["status"],
        "progress": project["progress"],
        "current_step": project["context"].get("current_step"),
        "completed_steps": project["context"].get("completed_steps", []),
        "error_message": project.get("error_message"),
        "updated_at": project["updated_at"],
    }

if __name__ == "__main__":
    print("🚀 Starting AG3NT Backend (Simplified)...")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Health Check: http://localhost:8000/health")
    print("📊 API Root: http://localhost:8000/api")
    print()
    
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
