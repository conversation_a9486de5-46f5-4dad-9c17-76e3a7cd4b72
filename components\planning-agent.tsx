"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { motion, AnimatePresence } from "framer-motion"
import { ExternalLink, MessageCircle, AlertCircle, Paperclip, X, Loader2 } from "lucide-react"
import type { PlanningTask, Question } from "@/types/planning"
import { ResultsView } from "@/components/results-view"
import Image from "next/image"
import { pythonBackendClient, type ProjectCreateRequest, type ProjectStepRequest } from "@/lib/python-backend-client"

const getBasePlanningTasks = (): PlanningTask[] => [
  { id: "analyze", title: "Analyze project requirements", completed: false },
  { id: "clarify", title: "Gather additional details", completed: false },
  { id: "summary", title: "Generate project summary", completed: false },
  { id: "techstack", title: "Select technology stack", completed: false },
  { id: "prd", title: "Create requirements document", completed: false },
]

const getOptionalTasks = (projectType: string): PlanningTask[] => {
  const tasks: PlanningTask[] = []

  // Add context profile for AI agents
  if (isAIAgentProject(projectType)) {
    tasks.push({ id: "context-profile", title: "Generate AI agent context profile", completed: false })
  }

  // Add database design for apps that need databases
  if (needsDatabaseStep(projectType)) {
    tasks.push({ id: "database", title: "Design database schema", completed: false })
  }

  return tasks
}

const getCommonTasks = (): PlanningTask[] => [
  { id: "wireframes", title: "Design UI wireframes", completed: false },
  { id: "design", title: "Create design guidelines", completed: false },
  { id: "filesystem", title: "Plan file structure", completed: false },
  { id: "workflow", title: "Define workflow logic", completed: false },
  { id: "tasks", title: "Break down implementation tasks", completed: false },
  { id: "scaffold", title: "Generate project scaffold", completed: false },
]

const isAIAgentProject = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("agent") ||
         lowerType.includes("bot") ||
         lowerType.includes("ai") ||
         lowerType === "ai agent"
}

const needsDatabaseStep = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("web") ||
         lowerType.includes("app") ||
         lowerType.includes("api") ||
         lowerType.includes("system")
}

const generateDynamicTasks = (projectType: string): PlanningTask[] => {
  return [
    ...getBasePlanningTasks(),
    ...getOptionalTasks(projectType),
    ...getCommonTasks()
  ]
}

export function PlanningAgent() {
  const [userPrompt, setUserPrompt] = useState("")
  const [hasStarted, setHasStarted] = useState(false)
  const [isInteractive, setIsInteractive] = useState(false)
  const [tasks, setTasks] = useState<PlanningTask[]>(getBasePlanningTasks())
  const [currentTaskIndex, setCurrentTaskIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<Record<string, any>>({})
  const [showResults, setShowResults] = useState(false)
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [questionAnswer, setQuestionAnswer] = useState("")
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({})
  const [planningContext, setPlanningContext] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [canRetry, setCanRetry] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [isProcessingImages, setIsProcessingImages] = useState(false)
  const [designStyleGuideState, setDesignStyleGuideState] = useState<string | null>(null)
  const taskListRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  // Removed isUsingFallback - using AI service only

  // Auto-scroll to current task
  useEffect(() => {
    if (taskListRef.current && currentTaskIndex >= 0) {
      const taskElements = taskListRef.current.querySelectorAll('[data-task-index]')
      const currentTaskElement = taskElements[currentTaskIndex] as HTMLElement

      if (currentTaskElement) {
        const container = taskListRef.current
        const containerHeight = container.clientHeight
        const taskTop = currentTaskElement.offsetTop
        const taskHeight = currentTaskElement.offsetHeight

        // Calculate scroll position to center the current task
        const scrollTop = taskTop - (containerHeight / 2) + (taskHeight / 2)

        container.scrollTo({
          top: Math.max(0, scrollTop),
          behavior: 'smooth'
        })
      }
    }
  }, [currentTaskIndex, tasks.length])

  const processNextTask = async (context: any) => {
    console.log(`processNextTask called with currentTaskIndex: ${currentTaskIndex}`)

    if (currentTaskIndex < 0 || currentTaskIndex >= tasks.length) {
      console.log("Invalid task index, finishing processing")
      setIsProcessing(false)
      return
    }

    const currentTask = tasks[currentTaskIndex]
    if (!currentTask) {
      console.log("No current task found, finishing processing")
      setIsProcessing(false)
      return
    }

    console.log(`Processing task: ${currentTask.id} (index: ${currentTaskIndex})`)
    console.log(`Context keys:`, Object.keys(context || {}))
    console.log(`Has design style guide:`, !!context.designStyleGuide)
    if (context?.designStyleGuide) {
      console.log("Design style guide length:", context.designStyleGuide.length)
    }

    // Ensure design style guide is preserved from multiple sources
    const designStyleGuide = context.designStyleGuide || planningContext?.designStyleGuide || designStyleGuideState
    console.log("Design style guide sources:")
    console.log("- context.designStyleGuide:", !!context.designStyleGuide)
    console.log("- planningContext?.designStyleGuide:", !!planningContext?.designStyleGuide)
    console.log("- designStyleGuideState:", !!designStyleGuideState)
    console.log("- final designStyleGuide:", !!designStyleGuide)
    if (designStyleGuide && !context.designStyleGuide) {
      console.log("Restoring design style guide from state/planningContext")
    }

    try {
      // Use the Python backend client instead of the old API
      const stepRequest: ProjectStepRequest = {
        step: currentTask.id,
        context: {
          ...context,
          prompt: userPrompt,
          isInteractive: isInteractive,
          userAnswers: userAnswers,
          designStyleGuide: designStyleGuide, // Always include design style guide if available
          hasImages: uploadedImages.length > 0,
        },
        answer: questionAnswer,
      }

      // Ensure we have a project ID from the planning context
      const projectId = planningContext?.projectId || context.projectId
      if (!projectId) {
        throw new Error("No project ID available. Please create a project first.")
      }

      const result = await pythonBackendClient.executeProjectStep(
        projectId,
        stepRequest
      )

      // The Python backend client already handles response parsing
      console.log(`Python backend response for ${currentTask.id}:`, result)

      // Validate result structure
      if (!result || typeof result !== "object") {
        throw new Error("Invalid result structure")
      }

      // Check if we need user input (Python backend format)
      if (result.needs_input && result.question && isInteractive) {
        console.log("Need user input:", result.question)
        setCurrentQuestion({ question: result.question, type: "text", optional: false })
        setPlanningContext({ ...context, ...result.result })
        return
      }

      // Check if there's an error
      if (result.status === "failed") {
        console.log("Step failed:", result.result?.error)
        setError(`Step processing issue: ${result.result?.error || "Unknown error"}`)
      }

      // Mark task as completed and store results
      setTasks((prev) => {
        const updated = prev.map((task, index) => (index === currentTaskIndex ? { ...task, completed: true } : task))
        console.log(`Marked task ${currentTaskIndex} (${currentTask.id}) as completed`)
        return updated
      })

      // If this is the analyze step, update the task list based on project type
      if (currentTask.id === "analyze" && result.result?.project_type) {
        const projectType = result.result.project_type
        console.log(`Project type detected: ${projectType}, updating task list...`)
        const newTasks = generateDynamicTasks(projectType)

        // Preserve completion status for already completed tasks
        const updatedTasks = newTasks.map((newTask, index) => {
          if (index < currentTaskIndex) {
            // Tasks before current should be completed
            return { ...newTask, completed: true }
          } else if (index === currentTaskIndex) {
            // Current task should be marked as completed since we just finished it
            return { ...newTask, completed: true }
          }
          return newTask
        })

        setTasks(updatedTasks)
        console.log(`Updated task list with ${updatedTasks.length} tasks for project type: ${projectType}`)
      }

      // Store the result data (Python backend format)
      if (result.result) {
        // Store the result returned from the Python backend
        setResults((prev) => ({ ...prev, [currentTask.id]: result.result }));
        console.log(`Stored result for ${currentTask.id}:`, result.result);
      } else {
        // Warn if no result data is available for any step.
        console.warn(`No result data for ${currentTask.id}`);
      }

      // Update planning context while preserving design style guide
      const preservedDesignStyleGuide = designStyleGuide || context.designStyleGuide || planningContext?.designStyleGuide || designStyleGuideState
      setPlanningContext({
        ...context,
        ...result.result,
        designStyleGuide: preservedDesignStyleGuide,
        projectId: context.projectId || result.project_id // Store project ID from backend
      })

      // Move to next task
      if (currentTaskIndex < tasks.length - 1) {
        console.log(`Moving to next task (${currentTaskIndex + 1})`)
        setTimeout(() => {
          setCurrentTaskIndex((prev) => prev + 1)
        }, 1000)
      } else {
        console.log("All tasks completed!")
        // Add delay to allow UI to update and show final task as completed
        setTimeout(() => {
          setIsProcessing(false)
        }, 1000)
      }
    } catch (error) {
      console.error("Failed to process step:", error)

      // Enhanced error handling for step processing
      let errorMessage = `AI step processing failed for ${currentTask.title}. `
      let shouldRetry = false

      if (error instanceof Error) {
        if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          shouldRetry = true
        } else if (error.message.includes("timeout")) {
          errorMessage += "Request timed out."
          shouldRetry = true
        } else if (error.message.includes("network")) {
          errorMessage += "Network error occurred."
          shouldRetry = true
        } else {
          errorMessage += error.message
        }
      } else {
        errorMessage += "Unknown error occurred."
      }

      setError(errorMessage + (shouldRetry ? " You can try again." : " Please check your AI service configuration."))
      setCanRetry(shouldRetry)

      // Stop processing on error - no fallback to mock data
      setIsProcessing(false)
    }
  }

  const handleStartPlanning = async () => {
    if (!userPrompt.trim()) return

    console.log("Starting planning process...")
    setHasStarted(true)
    setIsProcessing(true)
    setCurrentTaskIndex(0)
    setError(null)

    try {
      let designStyleGuide = null

      // If images are uploaded, use the design agent first
      if (uploadedImages.length > 0) {
        console.log("Processing uploaded images with design agent...")
        setIsProcessingImages(true)

        try {
          const formData = new FormData()
          uploadedImages.forEach((image, index) => {
            formData.append('images', image)
          })

          const designResponse = await fetch("/api/design-agent", {
            method: "POST",
            body: formData,
          })

          if (designResponse.ok) {
            const designResult = await designResponse.json()
            designStyleGuide = designResult.styleGuide
            setDesignStyleGuideState(designStyleGuide) // Store in state for persistence
            console.log("Design agent generated style guide - will be added when design step runs")
          } else {
            const errorText = await designResponse.text()
            console.warn("Design agent failed:", errorText)
            // Continue without style guide but show a warning
          }
        } catch (designError) {
          console.error("Design agent error:", designError)
          // Continue without style guide
        } finally {
          setIsProcessingImages(false)
        }
      }

      // Create project using Python backend
      const createRequest: ProjectCreateRequest = {
        prompt: userPrompt,
        isInteractive,
        answers: userAnswers,
        designStyleGuide, // Include the generated style guide
        hasImages: uploadedImages.length > 0,
      }

      const result = await pythonBackendClient.createProject(createRequest)
      console.log("Project created:", result)

      console.log("Using AI-powered planning mode")
      console.log("Setting planning context with design style guide:", !!designStyleGuide)
      setPlanningContext({
        projectId: result.project_id,
        prompt: userPrompt,
        isInteractive,
        userAnswers,
        designStyleGuide: designStyleGuide, // Ensure the design style guide is included and takes precedence
        hasImages: uploadedImages.length > 0,
      })
      // Start AI-based processing
      console.log("Setting currentTaskIndex to 0 and starting processing")
      setCurrentTaskIndex(0)
    } catch (error) {
      console.error("Failed to start AI planning:", error)

      // Enhanced error handling with specific error types
      let errorMessage = "AI planning failed. "
      let suggestion = ""

      if (error instanceof Error) {
        if (error.message.includes("401") || error.message.includes("403")) {
          errorMessage += "Authentication failed."
          suggestion = "Please check your OPENROUTER_API_KEY is valid and has sufficient credits."
        } else if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          suggestion = "Please wait a moment and try again."
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage += "Network connection failed."
          suggestion = "Please check your internet connection and try again."
        } else {
          errorMessage += error.message
          suggestion = "Please check your OPENROUTER_API_KEY and try again."
        }
      } else {
        errorMessage += "Unknown error occurred."
        suggestion = "Please refresh the page and try again."
      }

      setError(`${errorMessage} ${suggestion}`)
      // Do not fallback to mock data - show error instead
      setIsProcessing(false)
    }
  }

  // All mock data generation removed - using AI service only

  // All mock wireframe generation removed

  // All mock helper functions removed - using AI service only

  const retryCurrentStep = () => {
    setError(null)
    setCanRetry(false)
    setIsProcessing(true)

    // Retry the current step
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length) {
      setTimeout(() => processNextTask(planningContext || {}), 500)
    } else {
      // Retry from the beginning
      setTimeout(() => handleStartPlanning(), 500)
    }
  }

  const handleQuestionSubmit = async () => {
    if (currentQuestion) {
      setUserAnswers((prev) => ({ ...prev, [currentQuestion.id]: questionAnswer }))
      setQuestionAnswer("")
      setCurrentQuestion(null)

      // Continue processing with the answer using AI
      setTimeout(() => processNextTask(planningContext), 500)
    }
  }

  const handleQuestionKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleQuestionSubmit()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleStartPlanning()
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const imageFiles = Array.from(files).filter(file => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          return false
        }
        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`)
          return false
        }
        return true
      })

      // Limit total number of images to 5
      const currentCount = uploadedImages.length
      const newFiles = imageFiles.slice(0, Math.max(0, 5 - currentCount))

      if (newFiles.length < imageFiles.length) {
        alert(`Maximum 5 images allowed. Only the first ${newFiles.length} images were added.`)
      }

      setUploadedImages(prev => [...prev, ...newFiles])
    }
    // Reset the input value to allow uploading the same file again
    e.target.value = ''
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const triggerImageUpload = () => {
    fileInputRef.current?.click()
  }

  const clearAllImages = () => {
    setUploadedImages([])
  }

  useEffect(() => {
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length && isProcessing && !currentQuestion) {
      console.log(`useEffect triggered for task index: ${currentTaskIndex}, task: ${tasks[currentTaskIndex]?.id}`)
      processNextTask(planningContext || {})
    }
  }, [currentTaskIndex, isProcessing, currentQuestion])

  const openResults = () => {
    setShowResults(true)
  }

  const backToPlanning = () => {
    setShowResults(false)
  }

  if (showResults) {
    return <ResultsView results={results} userPrompt={userPrompt} onBack={backToPlanning} />
  }

  // Question overlay (only shown in interactive mode)
  if (currentQuestion && isInteractive) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center space-y-6">
            <div className="flex items-center justify-center gap-2 mb-4">
              <MessageCircle className="w-5 h-5 text-red-400" />
              <span className="text-sm text-gray-400">AG3NT is asking</span>
            </div>

            <h2 className="text-2xl font-light text-white">{currentQuestion.question}</h2>

            <div className="space-y-4">
              {currentQuestion.type === "text" ? (
                <Input
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full h-12 text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400"
                  autoFocus
                />
              ) : (
                <Textarea
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full min-h-[100px] text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400 resize-none"
                  autoFocus
                />
              )}

              <div className="flex gap-3">
                <Button
                  onClick={handleQuestionSubmit}
                  disabled={!questionAnswer.trim() && !currentQuestion.optional}
                  className="flex-1 h-12 bg-red-600 hover:bg-red-700 text-white rounded-none font-medium"
                >
                  Continue
                </Button>
                {currentQuestion.optional && (
                  <Button
                    onClick={() => {
                      setCurrentQuestion(null)
                      setTimeout(() => processNextTask(planningContext), 500)
                    }}
                    variant="outline"
                    className="h-12 px-6 border-gray-600 text-gray-300 hover:bg-gray-800 rounded-none"
                  >
                    Skip
                  </Button>
                )}
              </div>
            </div>

            {currentQuestion.optional && <p className="text-xs text-gray-500">This question is optional</p>}
          </motion.div>
        </div>
      </div>
    )
  }

  if (!hasStarted) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
        <div className="w-full max-w-2xl text-center space-y-8">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <div className="flex items-center justify-center gap-3 mb-6">
              <Image
                src="/AG3NT.png"
                alt="AG3NT"
                width={120}
                height={40}
                className="object-contain"
              />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Image Upload Preview */}
              {uploadedImages.length > 0 && (
                <div className="p-3 rounded-lg" style={{ backgroundColor: '#181818' }}>
                  {isProcessingImages && (
                    <div className="flex items-center gap-2 mb-3 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Analyzing images with AI...</span>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2">
                    {uploadedImages.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Upload ${index + 1}`}
                          className={`w-16 h-16 object-cover rounded border transition-opacity ${
                            isProcessingImages ? 'opacity-50' : 'opacity-100'
                          }`}
                          title={image.name}
                        />
                        {!isProcessingImages && (
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X size={12} />
                          </button>
                        )}
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b opacity-0 group-hover:opacity-100 transition-opacity truncate">
                          {image.name}
                        </div>
                      </div>
                    ))}
                  </div>
                  {uploadedImages.length > 0 && !isProcessingImages && (
                    <div className="mt-2 flex items-center justify-between">
                      <div className="text-xs text-gray-600">
                        {uploadedImages.length} image{uploadedImages.length > 1 ? 's' : ''} ready for AI analysis
                      </div>
                      <button
                        onClick={clearAllImages}
                        className="text-xs text-red-500 hover:text-red-700 underline"
                      >
                        Clear all
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Input with Paperclip and Send Button */}
              <div className="flex w-full items-center gap-0 rounded-full p-0 overflow-hidden" style={{padding:0}}>
                <Button
                  onClick={triggerImageUpload}
                  className="h-12 px-3 text-gray-600 bg-white border-0 rounded-l-full hover:bg-gray-50 transition-colors duration-200"
                  style={{ minWidth: 44 }}
                  title="Upload design reference images for AI analysis"
                  disabled={isProcessing || isProcessingImages}
                >
                  <Paperclip size={18} />
                  <span className="sr-only">Upload Images</span>
                </Button>
                <Input
                  value={userPrompt}
                  onChange={(e) => setUserPrompt(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="What would you like to build?"
                  className="flex-1 h-12 text-base text-black border-0 placeholder-gray-500 focus:ring-2 focus:ring-red-400 focus:ring-inset px-3 py-2"
                  autoFocus
                  style={{
                    paddingLeft: 12,
                    paddingRight: 8,
                    backgroundColor: '#ffffff',
                    color: '#000000'
                  }}
                />
                <Button
                  onClick={handleStartPlanning}
                  disabled={!userPrompt.trim()}
                  className="h-12 px-4 text-white rounded-r-full border-0 shadow-none hover:opacity-90 transition-opacity duration-200"
                  style={{
                    minWidth: 44,
                    backgroundColor: '#ff2d55'
                  }}
                >
                  <span className="sr-only">Start</span>
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                    <path d="M4 3v14l12-7L4 3z" fill="white"/>
                  </svg>
                </Button>
              </div>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>



            
          </motion.div>


        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black text-white p-4 flex flex-col relative">
      {/* Powered by AP3X - Bottom Right Corner */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="text-gray-400 text-xs" style={{opacity:0.4}}>
          <span style={{ fontWeight: 'bold' }}>
            Powered by{' '}
            <span style={{ color: 'white' }}>AP3</span>
            <span style={{ color: '#ff2d55', textShadow: '0 0 8px #ff2d55, 0 0 16px #ff2d55' }}>X</span>
          </span>
        </div>
      </div>
      <div className="flex-grow flex items-center justify-center">
        <div className="w-full max-w-md">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-2xl font-bold mb-4">
              AG3NT is Planning Your Project
            </h1>

            {error && (
              <div className="flex flex-col items-center justify-center gap-2 mt-2">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-400" />
                  <span className="text-xs text-yellow-400">{error}</span>
                </div>
                {canRetry && (
                  <Button
                    onClick={retryCurrentStep}
                    size="sm"
                    className="bg-red-600 hover:bg-red-700 text-xs px-3 py-1 h-6"
                  >
                    Retry
                  </Button>
                )}
              </div>
            )}
          </motion.div>

          <Card
            className="mb-8 w-full border-0"
            style={{
              backgroundColor: '#181818',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            }}
          >

            <CardContent className="p-6">
              <div
                ref={taskListRef}
                className="overflow-y-auto scrollbar-hide"
                style={{
                  maxHeight: '280px' // Reduced height by ~12.5% (from 320px to 280px)
                }}
              >
                <motion.ul layout className="space-y-4">
                  <AnimatePresence initial={false}>
                    {tasks.filter((_, originalIndex) => {
                      // Always show at least the first task, and show up to current + 1
                      const maxIndex = isProcessing ? Math.max(currentTaskIndex + 1, 0) : Math.max(currentTaskIndex, 0)
                      return originalIndex <= maxIndex
                    }).map((task) => {
                      const originalIndex = tasks.findIndex(t => t.id === task.id)
                      const isActive = originalIndex === currentTaskIndex && isProcessing
                      const isCompleted = task.completed
                      const isPending = originalIndex > currentTaskIndex

                      // Debug logging for task states
                      if (originalIndex <= 1) {
                        console.log(`Task ${originalIndex} (${task.id}): isActive=${isActive}, isCompleted=${isCompleted}, currentTaskIndex=${currentTaskIndex}, isProcessing=${isProcessing}`)
                      }

                      return (
                        <motion.li
                          key={task.id}
                          data-task-index={originalIndex}
                          layout
                          initial={{ opacity: 0, y: 50 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 50 }}
                          transition={{ type: "spring", stiffness: 500, damping: 30, duration: 0.3 }}
                          className={`py-3 cursor-pointer group ${isCompleted ? "opacity-60" : ""}`}
                        >
                          <div className="flex items-center space-x-3">
                            <motion.div
                              className="w-5 h-5 flex-shrink-0 relative"
                              animate={isActive ? { rotate: 360 } : { rotate: 0 }}
                              transition={
                                isActive ? { duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" } : { duration: 0 }
                              }
                            >
                              <svg
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="w-full h-full"
                              >
                                <circle
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke={isCompleted ? "#5e6ad2" : isPending ? "#4b5563" : "#ffffff"}
                                  strokeWidth="2"
                                  fill={isCompleted ? "#5e6ad2" : "none"}
                                  className={isActive ? "opacity-30" : ""}
                                />
                                {isActive && (
                                  <path
                                    d="M12 2C13.3132 2 14.6136 2.25866 15.8268 2.7612C17.0401 3.26375 18.1425 4.00035 19.0711 4.92893C19.9997 5.85752 20.7362 6.95991 21.2388 8.17317C21.7413 9.38642 22 10.6868 22 12"
                                    stroke="#ffffff"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                  />
                                )}
                                {isCompleted && !isActive && (
                                  <path
                                    d="M8 12L11 15L16 9"
                                    stroke="black"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                )}
                              </svg>
                            </motion.div>
                            <div className="flex-grow flex items-center justify-between overflow-hidden">
                              <span
                                className={`text-sm truncate transition-colors duration-300 ease-in-out ${
                                  isCompleted
                                    ? "text-gray-500"
                                    : isActive
                                      ? "text-white"
                                      : isPending
                                        ? "text-gray-400"
                                        : "text-gray-300"
                                }`}
                              >
                                {task.id === "design" && uploadedImages.length > 0
                                  ? "Analyze uploaded design references"
                                  : task.title}
                              </span>
                            </div>
                          </div>
                        </motion.li>
                      )
                    })}
                  </AnimatePresence>
                </motion.ul>
              </div>
            </CardContent>
          </Card>

          {!isProcessing && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center space-y-4"
            >
              <div className="text-green-400 font-semibold">✓ Planning Complete!</div>
              <Button
                onClick={openResults}
                className="w-full bg-red-600 hover:bg-red-700 flex items-center justify-center gap-2"
              >
                View AG3NT's Analysis
                <ExternalLink className="w-4 h-4" />
              </Button>
            </motion.div>
          )}


        </div>
      </div>
    </div>
  )
}
