#!/usr/bin/env node
/**
 * Test Backend Response Structure
 * 
 * This script tests what the backend actually returns for project creation
 */

const https = require('http');

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed, raw: data });
        } catch (e) {
          resolve({ status: res.statusCode, data: null, raw: data, parseError: e.message });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testBackendResponse() {
  console.log('🧪 Testing Backend Response Structure');
  console.log('====================================');
  
  try {
    const projectData = {
      prompt: "Test project for debugging",
      is_interactive: false,
      answers: {},
      design_style_guide: null,
      has_images: false
    };
    
    console.log('📝 Sending request:', JSON.stringify(projectData, null, 2));
    
    const response = await makeRequest('http://localhost:8000/api/v1/projects', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(projectData)
    });
    
    console.log('📊 Response Status:', response.status);
    console.log('📄 Raw Response:', response.raw);
    
    if (response.parseError) {
      console.log('❌ Parse Error:', response.parseError);
    } else {
      console.log('✅ Parsed Response:', JSON.stringify(response.data, null, 2));
      console.log('🔍 Project ID:', response.data?.project_id);
      console.log('🔍 Project ID Type:', typeof response.data?.project_id);
      console.log('🔍 Has project_id:', !!response.data?.project_id);
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
}

testBackendResponse();
